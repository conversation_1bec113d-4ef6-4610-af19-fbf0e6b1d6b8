<?php
/**
 * Template Name: All Destinations
 * 
 * Comprehensive list of all destinations
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('All Destinations', 'intercityzambia'); ?>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Explore every corner of Zambia. From bustling cities to remote towns, we connect you to all destinations across the country.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Quick Search -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-12">
            <div class="flex flex-col md:flex-row gap-4" x-data="{ search: '' }">
                <div class="flex-1">
                    <input 
                        type="text" 
                        placeholder="<?php _e('Search destinations...', 'intercityzambia'); ?>"
                        x-model="search"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                    >
                </div>
                <button class="bg-primary-800 hover:bg-primary-900 text-white px-6 py-3 rounded-lg font-medium transition-colors font-noto-sans">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <?php _e('Search', 'intercityzambia'); ?>
                </button>
            </div>
        </div>

        <!-- Destinations by Province -->
        <div class="mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 font-noto-serif">
                <?php _e('Browse by Province', 'intercityzambia'); ?>
            </h2>
            
            <?php
            $provinces = get_posts(array(
                'post_type' => 'province',
                'posts_per_page' => -1,
                'orderby' => 'title',
                'order' => 'ASC'
            ));
            
            foreach ($provinces as $province) :
                $province_cities = get_posts(array(
                    'post_type' => 'city',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => 'province',
                            'value' => $province->ID,
                            'compare' => '='
                        )
                    ),
                    'orderby' => 'title',
                    'order' => 'ASC'
                ));
                
                if (!empty($province_cities)) :
            ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6" x-data="{ expanded: false }">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 font-noto-serif">
                                    <?php echo esc_html($province->post_title); ?>
                                </h3>
                                <p class="text-gray-600 font-noto-sans">
                                    <?php printf(__('%d destinations', 'intercityzambia'), count($province_cities)); ?>
                                </p>
                            </div>
                            <button @click="expanded = !expanded" 
                                    class="flex items-center text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                                <span x-text="expanded ? '<?php _e('Show Less', 'intercityzambia'); ?>' : '<?php _e('Show All', 'intercityzambia'); ?>'"></span>
                                <svg class="w-4 h-4 ml-1 transition-transform duration-200" 
                                     :class="{ 'rotate-180': expanded }"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div x-show="expanded" x-collapse>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                <?php foreach ($province_cities as $city) : 
                                    $city_description = get_field('city_description', $city->ID);
                                    $city_population = get_field('city_population', $city->ID);
                                    
                                    // Get routes count
                                    $routes_count = count(get_posts(array(
                                        'post_type' => 'route',
                                        'posts_per_page' => -1,
                                        'meta_query' => array(
                                            'relation' => 'OR',
                                            array(
                                                'key' => 'departure_city',
                                                'value' => $city->ID,
                                                'compare' => '='
                                            ),
                                            array(
                                                'key' => 'arrival_city',
                                                'value' => $city->ID,
                                                'compare' => '='
                                            )
                                        )
                                    )));
                                ?>
                                    <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 font-noto-serif">
                                                <?php echo esc_html($city->post_title); ?>
                                            </h4>
                                            <?php if ($routes_count > 0) : ?>
                                                <span class="text-xs bg-primary-100 text-primary-800 px-2 py-1 rounded font-noto-sans">
                                                    <?php printf(__('%d routes', 'intercityzambia'), $routes_count); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($city_description) : ?>
                                            <p class="text-sm text-gray-600 mb-3 font-noto-sans">
                                                <?php echo esc_html(wp_trim_words($city_description, 15)); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <div class="flex items-center justify-between">
                                            <?php if ($city_population) : ?>
                                                <span class="text-xs text-gray-500 font-noto-sans">
                                                    <?php printf(__('Pop: %s', 'intercityzambia'), number_format($city_population)); ?>
                                                </span>
                                            <?php endif; ?>
                                            
                                            <div class="flex gap-2">
                                                <a href="<?php echo home_url('/routes?to=' . $city->ID); ?>" 
                                                   class="text-xs text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                                                    <?php _e('Routes', 'intercityzambia'); ?>
                                                </a>
                                                <a href="<?php echo home_url('/accommodation?city=' . $city->ID); ?>" 
                                                   class="text-xs text-secondary-600 hover:text-secondary-800 font-medium font-noto-sans">
                                                    <?php _e('Hotels', 'intercityzambia'); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        </div>

        <!-- Popular Destinations -->
        <div class="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-6 md:p-8">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                <?php _e('Most Popular Destinations', 'intercityzambia'); ?>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php
                // Get popular destinations based on route count
                $popular_destinations = get_posts(array(
                    'post_type' => 'city',
                    'posts_per_page' => 6,
                    'meta_query' => array(
                        array(
                            'key' => 'city_active',
                            'value' => '1',
                            'compare' => '='
                        )
                    )
                ));
                
                foreach ($popular_destinations as $destination) :
                    $routes_count = count(get_posts(array(
                        'post_type' => 'route',
                        'posts_per_page' => -1,
                        'meta_query' => array(
                            'relation' => 'OR',
                            array(
                                'key' => 'departure_city',
                                'value' => $destination->ID,
                                'compare' => '='
                            ),
                            array(
                                'key' => 'arrival_city',
                                'value' => $destination->ID,
                                'compare' => '='
                            )
                        )
                    )));
                ?>
                    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-bold text-gray-900 font-noto-serif">
                                <?php echo esc_html($destination->post_title); ?>
                            </h3>
                            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium font-noto-sans">
                                <?php printf(__('%d routes', 'intercityzambia'), $routes_count); ?>
                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="flex gap-3">
                                <a href="<?php echo home_url('/routes?to=' . $destination->ID); ?>" 
                                   class="text-sm bg-primary-100 text-primary-800 px-3 py-1 rounded-lg hover:bg-primary-200 transition-colors font-noto-sans">
                                    <?php _e('View Routes', 'intercityzambia'); ?>
                                </a>
                                <a href="<?php echo home_url('/city-breaks#' . sanitize_title($destination->post_title)); ?>" 
                                   class="text-sm bg-secondary-100 text-secondary-800 px-3 py-1 rounded-lg hover:bg-secondary-200 transition-colors font-noto-sans">
                                    <?php _e('City Break', 'intercityzambia'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>