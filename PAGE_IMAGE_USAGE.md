# Page Image Custom Field Usage Guide

## Overview
The `page_image` custom field has been added to all pages (except front page) and all custom post types. This field allows you to display an optional header image at the top of pages.

## Features Added

### 1. Image Sizes Registered
- `page-image`: 1280 x 500px (for page headers)
- `cards`: 640 x 360px (for card layouts)
- `gallery`: 1280 x 720px (for gallery images)

### 2. Custom Field Added
- **Field Name**: `page_image`
- **Type**: Image Upload
- **Required**: No (optional)
- **Locations**: All pages except front page + all custom post types
- **Minimum Size**: 640x250px
- **Maximum Size**: 2560x1000px
- **Instructions**: "Optional image to display at the top of the page. If not provided, the page content will occupy the full width. Recommended size: 1280x500px"

### 3. Template Functions
```php
// Check if page has image
if (intercity_has_page_image()) {
    // Display the image
}

// Get page image data
$page_image = intercity_get_page_image();

// Display page image with default settings
intercity_display_page_image();

// Display page image with custom size
intercity_display_page_image(null, 'cards');

// Get page image URL
$image_url = intercity_get_page_image_url();

// Get page image URL with custom size
$image_url = intercity_get_page_image_url(null, 'gallery');
```

### 4. CSS Classes
The theme automatically adds body classes:
- `has-page-image`: When page has a page image
- `no-page-image`: When page doesn't have a page image

### 5. Templates Updated
- `page.php`: Uses page_image instead of featured image
- `single.php`: New template for single posts and custom post types
- `single-station.php`: Updated to use page_image

## How to Use

### 1. Adding Images to Pages
1. Edit any page (except front page)
2. Scroll to the "Page Image" section
3. Upload or select an image
4. The image will automatically display at the top of the page

### 2. Adding Images to Custom Post Types
1. Edit any custom post type (province, city, station, bus_operator, etc.)
2. Look for the "Page Image" section
3. Upload or select an image
4. The image will display at the top of the single post template

### 3. Conditional Display
- **With Image**: Page displays header image with overlay text
- **Without Image**: Page displays gradient background with text
- **Content Layout**: Adjusts automatically based on presence of image

## Image Optimization
- Use the `page-image` size (1280x500px) for best results
- Images are automatically cropped to maintain aspect ratio
- Lazy loading is enabled for better performance
- Images include alt text for accessibility

## CSS Customization
The page image styles are included in `functions.php` and can be customized:

```css
.page-image-container {
    width: 100%;
    margin-bottom: 2rem;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.page-image:hover {
    transform: scale(1.02);
}
```

## Custom Implementation in Other Templates

To add page image support to other templates:

```php
<?php if (intercity_has_page_image()) : ?>
    <div class="page-image-header">
        <?php 
        $page_image = intercity_get_page_image();
        $image_url = isset($page_image['sizes']['page-image']) ? $page_image['sizes']['page-image'] : $page_image['url'];
        ?>
        <img src="<?php echo esc_url($image_url); ?>" 
             alt="<?php echo esc_attr($page_image['alt']); ?>" 
             class="page-image" 
             loading="lazy">
    </div>
<?php endif; ?>
```

## Notes
- Front page is excluded (will use slider as mentioned)
- All custom post types are included
- Images are responsive and mobile-friendly
- Fallback gracefully when no image is provided
- Uses the configured image size for optimal display