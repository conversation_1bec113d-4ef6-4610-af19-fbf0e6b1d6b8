<?php
/**
 * Check for duplicate meta keys in database
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

echo "<h1>Duplicate Meta Keys Check</h1>";

// Get a problematic page
$pages = get_posts(array(
    'post_type' => 'page',
    'posts_per_page' => 5,
    'post_status' => 'any'
));

if (empty($pages)) {
    echo "<p style='color: red;'>❌ No pages found to check</p>";
    exit;
}

global $wpdb;

foreach ($pages as $page) {
    echo "<h3>Page: {$page->post_title} (ID: {$page->ID})</h3>";
    
    // Check for duplicate _acf_changed entries
    $acf_duplicates = $wpdb->get_results($wpdb->prepare("
        SELECT meta_id, meta_key, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE post_id = %d 
        AND meta_key = '_acf_changed'
        ORDER BY meta_id
    ", $page->ID));
    
    if (count($acf_duplicates) > 1) {
        echo "<p style='color: red;'>❌ Found " . count($acf_duplicates) . " duplicate _acf_changed entries:</p>";
        echo "<ul>";
        foreach ($acf_duplicates as $duplicate) {
            echo "<li>Meta ID: {$duplicate->meta_id}, Value: {$duplicate->meta_value}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ No _acf_changed duplicates found</p>";
    }
    
    // Check for duplicate footnotes entries
    $footnote_duplicates = $wpdb->get_results($wpdb->prepare("
        SELECT meta_id, meta_key, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE post_id = %d 
        AND meta_key = 'footnotes'
        ORDER BY meta_id
    ", $page->ID));
    
    if (count($footnote_duplicates) > 1) {
        echo "<p style='color: red;'>❌ Found " . count($footnote_duplicates) . " duplicate footnotes entries:</p>";
        echo "<ul>";
        foreach ($footnote_duplicates as $duplicate) {
            echo "<li>Meta ID: {$duplicate->meta_id}, Value: {$duplicate->meta_value}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ No footnotes duplicates found</p>";
    }
    
    // Check for ANY duplicate meta keys for this post
    $all_duplicates = $wpdb->get_results($wpdb->prepare("
        SELECT meta_key, COUNT(*) as count
        FROM {$wpdb->postmeta} 
        WHERE post_id = %d 
        GROUP BY meta_key
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    ", $page->ID));
    
    if (!empty($all_duplicates)) {
        echo "<p style='color: orange;'>⚠️ Found duplicate meta keys:</p>";
        echo "<ul>";
        foreach ($all_duplicates as $duplicate) {
            echo "<li><strong>{$duplicate->meta_key}</strong>: {$duplicate->count} entries</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ No duplicate meta keys found</p>";
    }
    
    echo "<hr>";
}