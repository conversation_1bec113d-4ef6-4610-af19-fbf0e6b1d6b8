<?php
/**
 * Template Name: Routes Directory
 * 
 * Displays route search form and lists up to 10 routes with priority for major cities
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- <PERSON> Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('Bus Routes', 'intercityzambia'); ?>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Find the perfect bus route for your journey. Search by departure and arrival cities, or browse our most popular routes.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Route Search Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-12">
            <form method="GET" action="<?php echo home_url('/routes'); ?>" class="space-y-6" x-data="{ 
                fromCity: '<?php echo esc_attr(get_query_var('from', '')); ?>',
                toCity: '<?php echo esc_attr(get_query_var('to', '')); ?>',
                departureTime: '<?php echo esc_attr(get_query_var('departure_time', '')); ?>'
            }">
                
                <div class="text-center mb-6">
                    <h2 class="text-xl md:text-2xl font-bold text-gray-900 font-noto-serif">
                        <?php _e('Search Routes', 'intercityzambia'); ?>
                    </h2>
                    <p class="text-gray-600 mt-2 font-noto-sans">
                        <?php _e('Find bus routes between your desired cities', 'intercityzambia'); ?>
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- From City -->
                    <div>
                        <label for="from-city" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('From (City)', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="from-city" 
                            name="from" 
                            x-model="fromCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                            required
                        >
                            <option value=""><?php _e('Select departure city', 'intercityzambia'); ?></option>
                            <?php
                            // Get all cities that have routes departing from them
                            $departure_cities = get_posts(array(
                                'post_type' => 'city',
                                'posts_per_page' => -1,
                                'orderby' => 'title',
                                'order' => 'ASC',
                                'meta_query' => array(
                                    array(
                                        'key' => 'city_active',
                                        'value' => '1',
                                        'compare' => '='
                                    )
                                )
                            ));
                            
                            foreach ($departure_cities as $city) {
                                $selected = (get_query_var('from') == $city->ID) ? 'selected' : '';
                                echo '<option value="' . esc_attr($city->ID) . '" ' . $selected . '>' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- To City -->
                    <div>
                        <label for="to-city" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('To (City)', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="to-city" 
                            name="to" 
                            x-model="toCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                            required
                        >
                            <option value=""><?php _e('Select arrival city', 'intercityzambia'); ?></option>
                            <?php
                            // Get all cities that have routes arriving to them
                            $arrival_cities = get_posts(array(
                                'post_type' => 'city',
                                'posts_per_page' => -1,
                                'orderby' => 'title',
                                'order' => 'ASC',
                                'meta_query' => array(
                                    array(
                                        'key' => 'city_active',
                                        'value' => '1',
                                        'compare' => '='
                                    )
                                )
                            ));
                            
                            foreach ($arrival_cities as $city) {
                                $selected = (get_query_var('to') == $city->ID) ? 'selected' : '';
                                echo '<option value="' . esc_attr($city->ID) . '" ' . $selected . '>' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Departure Time -->
                    <div>
                        <label for="departure-time" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Departure Time', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="departure-time" 
                            name="departure_time" 
                            x-model="departureTime"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('Any time', 'intercityzambia'); ?></option>
                            <option value="morning" <?php echo (get_query_var('departure_time') == 'morning') ? 'selected' : ''; ?>><?php _e('Morning (6:00 - 11:59)', 'intercityzambia'); ?></option>
                            <option value="afternoon" <?php echo (get_query_var('departure_time') == 'afternoon') ? 'selected' : ''; ?>><?php _e('Afternoon (12:00 - 17:59)', 'intercityzambia'); ?></option>
                            <option value="evening" <?php echo (get_query_var('departure_time') == 'evening') ? 'selected' : ''; ?>><?php _e('Evening (18:00 - 23:59)', 'intercityzambia'); ?></option>
                            <option value="night" <?php echo (get_query_var('departure_time') == 'night') ? 'selected' : ''; ?>><?php _e('Night (00:00 - 5:59)', 'intercityzambia'); ?></option>
                        </select>
                    </div>
                </div>

                <!-- Search Button -->
                <div class="text-center">
                    <button 
                        type="submit" 
                        class="bg-primary-800 hover:bg-primary-900 text-white px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200 font-noto-sans"
                    >
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <?php _e('Search Routes', 'intercityzambia'); ?>
                    </button>
                </div>
            </form>
        </div>

        <?php
        // Handle search results
        $search_from = get_query_var('from');
        $search_to = get_query_var('to');
        $search_departure_time = get_query_var('departure_time');
        $showing_search_results = !empty($search_from) || !empty($search_to) || !empty($search_departure_time);
        
        if ($showing_search_results) {
            // Build meta query for search
            $meta_query = array('relation' => 'AND');
            
            if (!empty($search_from)) {
                $meta_query[] = array(
                    'key' => 'departure_city',
                    'value' => $search_from,
                    'compare' => '='
                );
            }
            
            if (!empty($search_to)) {
                $meta_query[] = array(
                    'key' => 'arrival_city',
                    'value' => $search_to,
                    'compare' => '='
                );
            }
            
            // Add active route filter
            $meta_query[] = array(
                'key' => 'active',
                'value' => '1',
                'compare' => '='
            );
            
            // Get search results
            $routes = get_posts(array(
                'post_type' => 'route',
                'posts_per_page' => 10,
                'meta_query' => $meta_query,
                'meta_key' => 'priority',
                'orderby' => 'meta_value_num title',
                'order' => 'DESC'
            ));
            
            $section_title = __('Search Results', 'intercityzambia');
            if (empty($routes)) {
                $section_title = __('No Routes Found', 'intercityzambia');
            }
        } else {
            // Get popular routes (high priority first)
            $routes = get_posts(array(
                'post_type' => 'route',
                'posts_per_page' => 10,
                'meta_query' => array(
                    array(
                        'key' => 'active',
                        'value' => '1',
                        'compare' => '='
                    )
                ),
                'meta_key' => 'priority',
                'orderby' => 'meta_value_num title',
                'order' => 'DESC'
            ));
            
            $section_title = __('Popular Routes', 'intercityzambia');
        }
        ?>

        <!-- Routes Results -->
        <div class="mb-16">
            <div class="flex items-center justify-between mb-8">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 font-noto-serif">
                    <?php echo esc_html($section_title); ?>
                </h2>
                <?php if ($showing_search_results && !empty($routes)) : ?>
                    <a href="<?php echo home_url('/routes'); ?>" class="text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                        <?php _e('View All Routes', 'intercityzambia'); ?>
                    </a>
                <?php endif; ?>
            </div>

            <?php if (empty($routes)) : ?>
                <div class="text-center py-12">
                    <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-xl font-bold text-gray-900 mb-2 font-noto-serif">
                        <?php _e('No Routes Found', 'intercityzambia'); ?>
                    </h3>
                    <p class="text-gray-600 mb-6 font-noto-sans">
                        <?php _e('We couldn\'t find any routes matching your search criteria. Try adjusting your search or browse our available routes.', 'intercityzambia'); ?>
                    </p>
                    <a href="<?php echo home_url('/routes'); ?>" class="btn-primary">
                        <?php _e('Browse All Routes', 'intercityzambia'); ?>
                    </a>
                </div>
            <?php else : ?>
                <div class="grid grid-cols-1 gap-6">
                    <?php foreach ($routes as $route) :
                        $departure_city = get_field('departure_city', $route->ID);
                        $arrival_city = get_field('arrival_city', $route->ID);
                        $departure_station = get_field('departure_station', $route->ID);
                        $arrival_station = get_field('arrival_station', $route->ID);
                        $operator = get_field('operator', $route->ID);
                        $distance = get_field('distance_km', $route->ID);
                        $duration = get_field('estimated_duration', $route->ID);
                        $description = get_field('description', $route->ID);
                        $priority = get_field('priority', $route->ID);
                    ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                                
                                <!-- Route Info -->
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <h3 class="text-xl font-bold text-gray-900 font-noto-serif">
                                            <?php echo esc_html($route->post_title); ?>
                                        </h3>
                                        <?php if ($priority && $priority > 50) : ?>
                                            <span class="ml-3 bg-secondary-100 text-secondary-800 px-3 py-1 rounded-full text-sm font-medium font-noto-sans">
                                                <?php _e('Popular', 'intercityzambia'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Route Details -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <div class="flex items-center text-gray-600 mb-2">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="font-medium font-noto-sans"><?php _e('From:', 'intercityzambia'); ?></span>
                                            </div>
                                            <p class="text-gray-900 font-noto-sans">
                                                <?php echo $departure_city ? esc_html($departure_city->post_title) : ''; ?>
                                                <?php if ($departure_station) : ?>
                                                    <span class="text-gray-600 text-sm block">
                                                        <?php echo esc_html($departure_station->post_title); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <div class="flex items-center text-gray-600 mb-2">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="font-medium font-noto-sans"><?php _e('To:', 'intercityzambia'); ?></span>
                                            </div>
                                            <p class="text-gray-900 font-noto-sans">
                                                <?php echo $arrival_city ? esc_html($arrival_city->post_title) : ''; ?>
                                                <?php if ($arrival_station) : ?>
                                                    <span class="text-gray-600 text-sm block">
                                                        <?php echo esc_html($arrival_station->post_title); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <!-- Additional Details -->
                                    <div class="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                                        <?php if ($operator) : ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                                </svg>
                                                <span class="font-noto-sans"><?php echo esc_html($operator->post_title); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($distance) : ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                                </svg>
                                                <span class="font-noto-sans"><?php echo esc_html($distance); ?> km</span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($duration) : ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="font-noto-sans"><?php echo esc_html($duration); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($description) : ?>
                                        <p class="text-gray-600 font-noto-sans">
                                            <?php echo esc_html(wp_trim_words($description, 20)); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex flex-col sm:flex-row gap-3 lg:flex-col lg:w-48">
                                    <a href="<?php echo get_permalink($route->ID); ?>" 
                                       class="btn-primary text-center">
                                        <?php _e('View Route', 'intercityzambia'); ?>
                                    </a>
                                    <a href="<?php echo home_url('/services?route=' . $route->ID); ?>" 
                                       class="bg-secondary-600 hover:bg-secondary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors text-center font-noto-sans">
                                        <?php _e('Book Now', 'intercityzambia'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Popular Destinations -->
        <?php if (!$showing_search_results) : ?>
            <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6 md:p-8">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                    <?php _e('Popular Destinations', 'intercityzambia'); ?>
                </h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <?php
                    // Get popular cities (those with most routes)
                    $popular_cities = get_posts(array(
                        'post_type' => 'city',
                        'posts_per_page' => 8,
                        'orderby' => 'title',
                        'order' => 'ASC',
                        'meta_query' => array(
                            array(
                                'key' => 'city_active',
                                'value' => '1',
                                'compare' => '='
                            )
                        )
                    ));
                    
                    foreach ($popular_cities as $city) :
                    ?>
                        <a href="<?php echo add_query_arg('to', $city->ID, home_url('/routes')); ?>" 
                           class="block bg-white rounded-lg p-4 hover:shadow-md transition-shadow duration-300 text-center">
                            <h3 class="font-medium text-gray-900 font-noto-serif">
                                <?php echo esc_html($city->post_title); ?>
                            </h3>
                            <p class="text-sm text-gray-600 mt-1 font-noto-sans">
                                <?php _e('Find routes', 'intercityzambia'); ?>
                            </p>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php get_footer(); ?>