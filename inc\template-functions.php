<?php
/**
 * Template Functions
 * 
 * Additional functions for the theme
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Calculate estimated reading time for posts
 */
function intercity_estimated_reading_time($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // Average reading speed is 200 words per minute
    
    return $reading_time;
}

/**
 * Display estimated reading time
 */
function intercity_reading_time($post_id = null) {
    $time = intercity_estimated_reading_time($post_id);
    
    if ($time == 1) {
        printf(__('%d min read', 'intercityzambia'), $time);
    } else {
        printf(__('%d min read', 'intercityzambia'), $time);
    }
}

/**
 * Custom excerpt with length control
 */
function intercity_custom_excerpt($length = 55, $post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $excerpt = get_post_field('post_excerpt', $post_id);
    
    if (empty($excerpt)) {
        $content = get_post_field('post_content', $post_id);
        $excerpt = wp_strip_all_tags($content);
    }
    
    if (str_word_count($excerpt) > $length) {
        $words = explode(' ', $excerpt);
        $excerpt = implode(' ', array_slice($words, 0, $length)) . '...';
    }
    
    return $excerpt;
}

/**
 * Get featured posts
 */
function intercity_get_featured_posts($count = 3) {
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => $count,
        'meta_query' => array(
            array(
                'key' => '_featured_post',
                'value' => '1',
                'compare' => '='
            )
        )
    );
    
    return get_posts($args);
}

/**
 * Get popular routes
 */
function intercity_get_popular_routes($count = 6) {
    $args = array(
        'post_type' => 'bus_route',
        'posts_per_page' => $count,
        'meta_query' => array(
            array(
                'key' => '_route_popularity',
                'value' => 0,
                'compare' => '>'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_route_popularity',
        'order' => 'DESC'
    );
    
    $routes = get_posts($args);
    
    // If no popular routes found, get recent ones
    if (empty($routes)) {
        $args = array(
            'post_type' => 'bus_route',
            'posts_per_page' => $count,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        $routes = get_posts($args);
    }
    
    return $routes;
}

/**
 * Get bus operators
 */
function intercity_get_bus_operators($count = -1) {
    $args = array(
        'post_type' => 'bus_operator',
        'posts_per_page' => $count,
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Format price in Zambian Kwacha
 */
function intercity_format_price($price) {
    return 'K' . number_format($price, 2);
}

/**
 * Format time for display
 */
function intercity_format_time($time) {
    return date('g:i A', strtotime($time));
}

/**
 * Format duration
 */
function intercity_format_duration($minutes) {
    $hours = floor($minutes / 60);
    $mins = $minutes % 60;
    
    if ($hours > 0) {
        return sprintf('%dh %02dmin', $hours, $mins);
    } else {
        return sprintf('%dmin', $mins);
    }
}

/**
 * Get route distance
 */
function intercity_get_route_distance($from, $to) {
    // This would typically connect to a mapping service
    // For now, return placeholder data
    $distances = array(
        'lusaka-kitwe' => 320,
        'kitwe-lusaka' => 320,
        'lusaka-livingstone' => 470,
        'livingstone-lusaka' => 470,
        'lusaka-chipata' => 580,
        'chipata-lusaka' => 580,
        'kitwe-ndola' => 45,
        'ndola-kitwe' => 45,
        'lusaka-kabwe' => 135,
        'kabwe-lusaka' => 135,
    );
    
    $key = strtolower($from . '-' . $to);
    return isset($distances[$key]) ? $distances[$key] : 0;
}

/**
 * Get route travel time estimate
 */
function intercity_get_travel_time($distance_km) {
    // Assume average speed of 60 km/h including stops
    $hours = $distance_km / 60;
    return round($hours * 60); // Return in minutes
}

/**
 * Check if user can book tickets
 */
function intercity_can_book_tickets() {
    // Add logic to check if booking is available
    // Could check business hours, route availability, etc.
    return true;
}

/**
 * Generate booking reference
 */
function intercity_generate_booking_reference() {
    return 'ICZ' . date('Ymd') . '-' . strtoupper(wp_generate_password(6, false));
}

/**
 * Get Zambian cities/towns
 */
function intercity_get_locations() {
    return array(
        'lusaka' => __('Lusaka', 'intercityzambia'),
        'kitwe' => __('Kitwe', 'intercityzambia'),
        'ndola' => __('Ndola', 'intercityzambia'),
        'kabwe' => __('Kabwe', 'intercityzambia'),
        'livingstone' => __('Livingstone', 'intercityzambia'),
        'chipata' => __('Chipata', 'intercityzambia'),
        'kasama' => __('Kasama', 'intercityzambia'),
        'solwezi' => __('Solwezi', 'intercityzambia'),
        'choma' => __('Choma', 'intercityzambia'),
        'mongu' => __('Mongu', 'intercityzambia'),
        'mansa' => __('Mansa', 'intercityzambia'),
        'kasempa' => __('Kasempa', 'intercityzambia'),
    );
}

/**
 * Get bus types
 */
function intercity_get_bus_types() {
    return array(
        'standard' => __('Standard', 'intercityzambia'),
        'express' => __('Express', 'intercityzambia'),
        'luxury' => __('Luxury', 'intercityzambia'),
        'vip' => __('VIP', 'intercityzambia'),
        'sleeper' => __('Sleeper', 'intercityzambia'),
    );
}

/**
 * Check if route exists
 */
function intercity_route_exists($from, $to, $date = null) {
    $args = array(
        'post_type' => 'bus_route',
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_departure_location',
                'value' => $from,
                'compare' => '='
            ),
            array(
                'key' => '_arrival_location',
                'value' => $to,
                'compare' => '='
            )
        )
    );
    
    if ($date) {
        $args['meta_query'][] = array(
            'key' => '_available_dates',
            'value' => $date,
            'compare' => 'LIKE'
        );
    }
    
    $routes = get_posts($args);
    return !empty($routes);
}

/**
 * Get route schedules for a specific date
 */
function intercity_get_route_schedules($from, $to, $date) {
    // This would query the database for actual schedules
    // For now, return mock data
    return array(
        array(
            'id' => 1,
            'operator' => 'Zambia Express',
            'departure_time' => '06:00',
            'arrival_time' => '11:30',
            'duration' => 330, // minutes
            'price' => 120,
            'bus_type' => 'Express',
            'seats_available' => 15
        ),
        array(
            'id' => 2,
            'operator' => 'Power Tools Bus',
            'departure_time' => '08:00',
            'arrival_time' => '13:15',
            'duration' => 315,
            'price' => 150,
            'bus_type' => 'Luxury',
            'seats_available' => 8
        ),
        array(
            'id' => 3,
            'operator' => 'Juldan Motors',
            'departure_time' => '14:00',
            'arrival_time' => '19:45',
            'duration' => 345,
            'price' => 110,
            'bus_type' => 'Standard',
            'seats_available' => 22
        )
    );
}

/**
 * Send booking confirmation email
 */
function intercity_send_booking_confirmation($booking_id, $email) {
    $booking = get_post($booking_id);
    if (!$booking) {
        return false;
    }
    
    $subject = sprintf(__('Booking Confirmation - %s', 'intercityzambia'), get_post_meta($booking_id, '_booking_reference', true));
    
    $message = sprintf(
        __('Dear %s,

Your bus booking has been confirmed!

Booking Details:
- Reference: %s
- Route: %s to %s
- Date: %s
- Time: %s
- Operator: %s
- Seats: %s
- Total Amount: %s

Please arrive at the departure station at least 30 minutes before departure time.

Safe travels!

Intercity Zambia Team', 'intercityzambia'),
        get_post_meta($booking_id, '_passenger_name', true),
        get_post_meta($booking_id, '_booking_reference', true),
        get_post_meta($booking_id, '_departure_location', true),
        get_post_meta($booking_id, '_arrival_location', true),
        get_post_meta($booking_id, '_travel_date', true),
        get_post_meta($booking_id, '_departure_time', true),
        get_post_meta($booking_id, '_bus_operator', true),
        get_post_meta($booking_id, '_number_of_seats', true),
        intercity_format_price(get_post_meta($booking_id, '_total_amount', true))
    );
    
    return wp_mail($email, $subject, $message);
}

/**
 * Validate Zambian phone number
 */
function intercity_validate_phone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Zambian number
    if (preg_match('/^(260|0)?[789][0-9]{8}$/', $phone)) {
        return true;
    }
    
    return false;
}

/**
 * Format Zambian phone number
 */
function intercity_format_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 9 && in_array($phone[0], ['7', '8', '9'])) {
        $phone = '260' . $phone;
    } elseif (strlen($phone) == 10 && $phone[0] == '0') {
        $phone = '260' . substr($phone, 1);
    }
    
    return $phone;
}

/**
 * Get post views count
 */
function intercity_get_post_views($post_id) {
    $count = get_post_meta($post_id, '_post_views', true);
    return $count ? $count : 0;
}

/**
 * Increment post views
 */
function intercity_increment_post_views($post_id) {
    $current_views = intercity_get_post_views($post_id);
    $new_views = $current_views + 1;
    update_post_meta($post_id, '_post_views', $new_views);
}

/**
 * Track route searches (for analytics)
 */
function intercity_track_route_search($from, $to, $date) {
    // Log search for analytics
    $searches = get_option('intercity_route_searches', array());
    $key = $from . '-' . $to;
    
    if (!isset($searches[$key])) {
        $searches[$key] = 0;
    }
    
    $searches[$key]++;
    update_option('intercity_route_searches', $searches);
}

/**
 * Get most searched routes
 */
function intercity_get_popular_searches($limit = 10) {
    $searches = get_option('intercity_route_searches', array());
    arsort($searches);
    return array_slice($searches, 0, $limit, true);
}

/**
 * Check if current page is related to bookings
 */
function intercity_is_booking_page() {
    return is_singular('booking') || is_post_type_archive('booking') || 
           (is_page() && get_post_meta(get_the_ID(), '_booking_page', true));
}

/**
 * Get breadcrumb navigation
 */
function intercity_get_breadcrumbs() {
    $breadcrumbs = array();
    
    // Home
    $breadcrumbs[] = array(
        'title' => __('Home', 'intercityzambia'),
        'url' => home_url(),
        'current' => false
    );
    
    if (is_home()) {
        $breadcrumbs[] = array(
            'title' => __('Blog', 'intercityzambia'),
            'url' => '',
            'current' => true
        );
    } elseif (is_single()) {
        if (get_post_type() == 'post') {
            $breadcrumbs[] = array(
                'title' => __('Blog', 'intercityzambia'),
                'url' => get_permalink(get_option('page_for_posts')),
                'current' => false
            );
        }
        
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url' => '',
            'current' => true
        );
    } elseif (is_page()) {
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url' => '',
            'current' => true
        );
    } elseif (is_category()) {
        $breadcrumbs[] = array(
            'title' => __('Blog', 'intercityzambia'),
            'url' => get_permalink(get_option('page_for_posts')),
            'current' => false
        );
        
        $breadcrumbs[] = array(
            'title' => single_cat_title('', false),
            'url' => '',
            'current' => true
        );
    }
    
    return $breadcrumbs;
}

// Hook to track post views
add_action('wp_head', function() {
    if (is_single() && !is_user_logged_in()) {
        intercity_increment_post_views(get_the_ID());
    }
});

/**
 * Add admin CSS for better styling
 */
function intercity_admin_custom_styles() {
    ?>
    <style>
    .bus-title-help .acf-message {
        background: #e7f3ff;
        border-left: 4px solid #0073aa;
        padding: 15px;
        margin-bottom: 20px;
    }
    .bus-title-help .acf-message strong {
        color: #0073aa;
    }
    .bus-title-help ul {
        margin-top: 10px;
        padding-left: 20px;
    }
    .bus-title-help li {
        margin-bottom: 5px;
        font-family: monospace;
        background: #f8f9fa;
        padding: 3px 6px;
        border-radius: 3px;
    }
    
    /* Route Management Styles */
    .route-name-field input {
        background: #f8f9fa !important;
        border: 2px solid #0073aa !important;
        color: #0073aa !important;
        font-weight: bold !important;
    }
    
    .route-name-field .acf-label {
        color: #0073aa !important;
        font-weight: bold !important;
    }
    
    .departure-city-field,
    .arrival-city-field {
        position: relative;
    }
    
    .departure-city-field::after,
    .arrival-city-field::after {
        content: "🚌";
        position: absolute;
        top: 25px;
        right: 15px;
        font-size: 18px;
        pointer-events: none;
        z-index: 1;
    }
    
    .departure-station-field select:disabled,
    .arrival-station-field select:disabled {
        background: #f5f5f5 !important;
        color: #666 !important;
        opacity: 0.8;
        cursor: not-allowed;
    }
    
    .departure-station-field select:disabled::after,
    .arrival-station-field select:disabled::after {
        content: "⏳";
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }
    
    /* Route field group styling */
    .acf-field-group .acf-field[data-name="departure_city"],
    .acf-field-group .acf-field[data-name="arrival_city"] {
        background: #f0f8ff;
        border-left: 4px solid #0073aa;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 3px;
    }
    
    .acf-field-group .acf-field[data-name="departure_station"],
    .acf-field-group .acf-field[data-name="arrival_station"] {
        background: #f8f9fa;
        border-left: 4px solid #6c757d;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 3px;
    }
    </style>
    <?php
}
add_action('admin_head', 'intercity_admin_custom_styles');

/**
 * Add admin JavaScript for service capacity auto-population
 */
function intercity_admin_service_capacity_script() {
    global $post;
    
    // Only load on service post type edit screens
    if (!$post || $post->post_type !== 'service') {
        return;
    }
    
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Function to update seats based on selected bus
        function updateSeatsFromBus() {
            var busId = $('select[name="acf[field_service_bus]"]').val();
            
            if (busId) {
                // AJAX call to get bus capacity
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_bus_capacity',
                        bus_id: busId,
                        nonce: '<?php echo wp_create_nonce('get_bus_capacity'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            var capacity = response.data.capacity;
                            var bookedSeats = parseInt($('input[name="acf[field_booked_seats]"]').val()) || 0;
                            var availableSeats = capacity - bookedSeats;
                            
                            // Update the fields
                            $('input[name="acf[field_total_seats]"]').val(capacity);
                            $('input[name="acf[field_available_seats]"]').val(Math.max(0, availableSeats));
                        }
                    }
                });
            }
        }
        
        // Function to update available seats when booked seats change
        function updateAvailableSeats() {
            var totalSeats = parseInt($('input[name="acf[field_total_seats]"]').val()) || 0;
            var bookedSeats = parseInt($('input[name="acf[field_booked_seats]"]').val()) || 0;
            var availableSeats = totalSeats - bookedSeats;
            
            $('input[name="acf[field_available_seats]"]').val(Math.max(0, availableSeats));
        }
        
        // Trigger when bus is selected
        $('select[name="acf[field_service_bus]"]').on('change', updateSeatsFromBus);
        
        // Trigger when booked seats change
        $('input[name="acf[field_booked_seats]"]').on('input', updateAvailableSeats);
        
        // Initial update on page load
        updateSeatsFromBus();
    });
    </script>
    <?php
}
add_action('admin_footer', 'intercity_admin_service_capacity_script');

/**
 * Add Route Management JavaScript
 */
function intercity_admin_route_management_script() {
    global $post_type;
    
    // Only load on route post type
    if ($post_type !== 'route') {
        return;
    }
    
    // Enqueue HTMX if available
    wp_enqueue_script('htmx', get_template_directory_uri() . '/assets/js/htmx.min.js', array(), '1.8.4', true);
    
    // Enqueue Alpine.js if available
    wp_enqueue_script('alpine', get_template_directory_uri() . '/assets/js/alpine.min.js', array(), '3.12.0', true);
    
    ?>
    <script type="text/javascript">
    // Route Management System - Define globally for SCF compatibility - v<?php echo time(); ?>
    console.log('Script loading: <?php echo time(); ?>');
    
    // Simple test first
    window.RouteManager = {
        test: function() {
            console.log('RouteManager test function works!');
        },
        
        // Initialize the route manager
        init: function() {
            console.log('RouteManager.init() called');
            this.setupCityStationEvents();
        },
        
        // Setup city-station events
        setupCityStationEvents: function() {
            console.log('Setting up city-station events');
            var self = this;
            
            // Find departure city field
            var departureCityField = document.querySelector('#acf-field_departure_city');
            console.log('Departure city field:', departureCityField);
            
            if (departureCityField) {
                departureCityField.addEventListener('change', function() {
                    console.log('Departure city changed:', this.value);
                    self.updateStationField(this.value, 'departure_station');
                });
            }
            
            // Find arrival city field
            var arrivalCityField = document.querySelector('#acf-field_arrival_city');
            console.log('Arrival city field:', arrivalCityField);
            
            if (arrivalCityField) {
                arrivalCityField.addEventListener('change', function() {
                    console.log('Arrival city changed:', this.value);
                    self.updateStationField(this.value, 'arrival_station');
                });
            }
        },
        
        // Update station field with cities
        updateStationField: function(cityId, stationFieldName) {
            console.log('Updating station field:', stationFieldName, 'for city:', cityId);
            var stationField = document.querySelector('#acf-field_' + stationFieldName);
            
            if (stationField) {
                stationField.innerHTML = '<option value="">Loading stations...</option>';
                console.log('Station field updated:', stationField);
            } else {
                console.log('Station field not found:', stationFieldName);
            }
        }
    };
    
    // Test that RouteManager is defined
    console.log('RouteManager defined:', typeof window.RouteManager, window.RouteManager);
    
    // Initialize RouteManager when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Ready - RouteManager object:', window.RouteManager);
        if (typeof window.RouteManager !== 'undefined') {
            console.log('Initializing RouteManager from main script...');
            window.RouteManager.init();
        } else {
            console.log('RouteManager not found in main script');
        }
    });
    </script>
    <?php
}
add_action('admin_head', 'intercity_admin_route_management_script');

/**
 * Add Route Management JavaScript for admin
 */
function intercity_route_management_script() {
    global $post_type;
    
    // Only load on route post type
    if ($post_type !== 'route') {
        return;
    }
    
    ?>
    <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Debug: Find all form fields
        function debugFormFields() {
            console.log('=== SCF Field Debug v<?php echo time(); ?> ===');
            console.log('All select elements:', document.querySelectorAll('select'));
            console.log('City fields:', document.querySelectorAll('select[name*="city"]'));
            console.log('Station fields:', document.querySelectorAll('select[name*="station"]'));
            console.log('Data-name attributes:', document.querySelectorAll('[data-name]'));
            console.log('RouteManager from debug script:', window.RouteManager);
            console.log('=====================');
        }
        
        // Run debug to help troubleshoot field detection
        setTimeout(debugFormFields, 100);
    });
    </script>
    <?php
}
add_action('admin_head', 'intercity_route_management_script');

/**
 * AJAX Handler: Get stations by city
 */
function intercity_ajax_get_stations_by_city() {
    // Debug logging
    error_log('AJAX Handler Called: get_stations_by_city');
    error_log('POST Data: ' . print_r($_POST, true));
    
    // Verify nonce (allow fallback for debugging)
    if (isset($_POST['nonce']) && $_POST['nonce'] !== 'default_nonce') {
        if (!wp_verify_nonce($_POST['nonce'], 'intercity_admin_nonce')) {
            error_log('Nonce verification failed');
            wp_send_json_error('Invalid nonce');
            return;
        }
    }
    
    $city_id = intval($_POST['city_id']);
    
    if (!$city_id) {
        error_log('Invalid city ID: ' . $_POST['city_id']);
        wp_send_json_error('Invalid city ID');
        return;
    }
    
    error_log('Looking for stations in city: ' . $city_id);
    
    // Try multiple possible meta keys for city
    $possible_keys = array('field_station_city', 'city', 'station_city');
    $stations = array();
    
    foreach ($possible_keys as $key) {
        $stations = get_posts(array(
            'post_type' => 'station',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => $key,
                    'value' => $city_id,
                    'compare' => '='
                )
            )
        ));
        
        if (!empty($stations)) {
            error_log('Found stations with meta key: ' . $key);
            break;
        }
    }
    
    error_log('Found stations: ' . count($stations));
    
    $station_options = array();
    if ($stations) {
        foreach ($stations as $station) {
            $station_options[] = array(
                'value' => $station->ID,
                'label' => $station->post_title
            );
        }
    }
    
    error_log('Station options: ' . print_r($station_options, true));
    
    wp_send_json_success($station_options);
}
add_action('wp_ajax_get_stations_by_city', 'intercity_ajax_get_stations_by_city');
add_action('wp_ajax_nopriv_get_stations_by_city', 'intercity_ajax_get_stations_by_city');

/**
 * Filter station fields to show empty options initially
 */
function intercity_filter_station_field_query($args, $field, $post_id) {
    // Only filter station fields in route post type
    if (!$post_id || get_post_type($post_id) !== 'route') {
        return $args;
    }
    
    // Only filter departure_station and arrival_station fields
    if (!isset($field['name']) || ($field['name'] !== 'departure_station' && $field['name'] !== 'arrival_station')) {
        return $args;
    }
    
    // For new posts (auto-draft), return empty results
    if (get_post_status($post_id) === 'auto-draft') {
        $args['post__in'] = array(0);
        return $args;
    }
    
    return $args;
}
add_filter('acf/fields/post_object/query', 'intercity_filter_station_field_query', 10, 3);

/**
 * Auto-populate route name when cities are selected
 */
function intercity_auto_populate_route_name($post_id) {
    // Only for route post type
    if (get_post_type($post_id) !== 'route') {
        return;
    }
    
    // Get departure and arrival cities
    $departure_city = get_field('departure_city', $post_id);
    $arrival_city = get_field('arrival_city', $post_id);
    
    // Generate route name if both cities are selected
    if ($departure_city && $arrival_city) {
        $route_name = $departure_city->post_title . ' - ' . $arrival_city->post_title;
        
        // Update the route name field
        update_field('route_name', $route_name, $post_id);
    }
}
// Only add ACF save hook if ACF/SCF is active
if (function_exists('get_field')) {
    add_action('acf/save_post', 'intercity_auto_populate_route_name', 20);
}

/**
 * AJAX Handler: Get city name by ID
 */
function intercity_ajax_get_city_name() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'get_city_name')) {
        wp_die('Security check failed');
    }
    
    $city_id = intval($_POST['city_id']);
    
    if (!$city_id) {
        wp_send_json_error('Invalid city ID');
    }
    
    $city = get_post($city_id);
    
    if (!$city || $city->post_type !== 'city') {
        wp_send_json_error('City not found');
    }
    
    wp_send_json_success($city->post_title);
}
add_action('wp_ajax_get_city_name', 'intercity_ajax_get_city_name');

/**
 * Admin notice for Route functionality
 */
function intercity_route_admin_notice() {
    global $post_type;
    
    if ($post_type !== 'route') {
        return;
    }
    
    ?>
    <div class="notice notice-info is-dismissible">
        <h3>🚌 Route Management Guide</h3>
        <p><strong>How to create a route:</strong></p>
        <ol>
            <li><strong>Select Departure City:</strong> Choose the starting city - stations will auto-populate</li>
            <li><strong>Select Arrival City:</strong> Choose the destination city - stations will auto-populate</li>
            <li><strong>Route Name:</strong> Will be auto-generated (e.g., "Lusaka - Livingstone")</li>
            <li><strong>Select Operator:</strong> Choose the bus company that operates this route</li>
            <li><strong>Optional Fields:</strong> Add distance, duration, and descriptions as needed</li>
        </ol>
        <p><strong>📍 Note:</strong> If a city has multiple stations, you'll see a dropdown to choose. If only one station exists, it will be auto-selected.</p>
    </div>
    <?php
}
add_action('admin_notices', 'intercity_route_admin_notice');



/**
 * Add Station Map Management JavaScript
 */
function intercity_admin_station_map_script() {
    global $post_type;
    
    // Only load on station post type
    if ($post_type !== 'station') {
        return;
    }
    
    ?>
    <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Station Map Management System
        window.stationMapManager = {
            // Initialize the map manager
            init() {
                this.bindMapEvents();
                this.bindCoordinateEvents();
            },
            
            // Bind Google Map events
            bindMapEvents() {
                // Listen for ACF Google Map field changes
                if (typeof acf !== 'undefined') {
                    acf.addAction('google_map_change', function(map, marker, field) {
                        if (field.data.name === 'map_location') {
                            const position = marker.getPosition();
                            const lat = position.lat();
                            const lng = position.lng();
                            
                            // Update latitude and longitude fields
                            const latField = document.querySelector('[data-name="latitude"] input');
                            const lngField = document.querySelector('[data-name="longitude"] input');
                            
                            if (latField) {
                                latField.value = lat.toFixed(6);
                                latField.dispatchEvent(new Event('input'));
                            }
                            
                            if (lngField) {
                                lngField.value = lng.toFixed(6);
                                lngField.dispatchEvent(new Event('input'));
                            }
                            
                            console.log('Map location updated:', { lat, lng });
                        }
                    });
                }
            },
            
            // Bind coordinate field events
            bindCoordinateEvents() {
                const self = this;
                
                // Listen for manual coordinate changes
                const latField = document.querySelector('[data-name="latitude"] input');
                const lngField = document.querySelector('[data-name="longitude"] input');
                
                if (latField && lngField) {
                    const updateMapFromCoordinates = function() {
                        const lat = parseFloat(latField.value);
                        const lng = parseFloat(lngField.value);
                        
                        if (!isNaN(lat) && !isNaN(lng)) {
                            self.updateMapMarker(lat, lng);
                        }
                    };
                    
                    latField.addEventListener('blur', updateMapFromCoordinates);
                    lngField.addEventListener('blur', updateMapFromCoordinates);
                }
            },
            
            // Update map marker from coordinates
            updateMapMarker(lat, lng) {
                if (typeof acf !== 'undefined') {
                    const mapField = acf.getField('field_station_map_location');
                    if (mapField) {
                        const map = mapField.map;
                        const marker = mapField.marker;
                        
                        if (map && marker) {
                            const position = new google.maps.LatLng(lat, lng);
                            marker.setPosition(position);
                            map.setCenter(position);
                            
                            console.log('Map marker updated from coordinates:', { lat, lng });
                        }
                    }
                }
            }
        };
        
        // Initialize when ACF is ready
        if (typeof acf !== 'undefined') {
            acf.addAction('ready', function() {
                stationMapManager.init();
            });
        } else {
            // Fallback if ACF isn't loaded yet
            setTimeout(function() {
                if (typeof acf !== 'undefined') {
                    stationMapManager.init();
                }
            }, 1000);
        }
    });
    </script>
    
    <style>
    /* Station Map Field Styling */
    .station-map-field {
        border: 2px solid #0073aa;
        border-radius: 8px;
        padding: 15px;
        background: #f8f9fa;
        margin-top: 15px;
    }
    
    .station-map-field .acf-label {
        color: #0073aa;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .station-map-field .acf-google-map {
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    /* Coordinate fields styling */
    .acf-field[data-name="latitude"],
    .acf-field[data-name="longitude"] {
        position: relative;
    }
    
    .acf-field[data-name="latitude"]::after {
        content: "📍";
        position: absolute;
        top: 25px;
        right: 15px;
        font-size: 16px;
        pointer-events: none;
    }
    
    .acf-field[data-name="longitude"]::after {
        content: "🌍";
        position: absolute;
        top: 25px;
        right: 15px;
        font-size: 16px;
        pointer-events: none;
    }
    
    /* Map help text */
    .station-map-field .description {
        background: #e8f4f8;
        border-left: 4px solid #0073aa;
        padding: 10px;
        margin-top: 10px;
        border-radius: 3px;
        font-size: 13px;
    }
    </style>
    <?php
}
add_action('admin_head', 'intercity_admin_station_map_script');

/**
 * Sync Google Map field with coordinates when saving station
 */
function intercity_sync_station_map_coordinates($post_id) {
    // Only for station post type
    if (get_post_type($post_id) !== 'station') {
        return;
    }
    
    // Avoid infinite loops
    if (wp_is_post_revision($post_id)) {
        return;
    }
    
    // Get latitude and longitude
    $latitude = get_field('latitude', $post_id);
    $longitude = get_field('longitude', $post_id);
    
    // Get current map location
    $map_location = get_field('map_location', $post_id);
    
    // If we have coordinates but no map location, create map location
    if ($latitude && $longitude && !$map_location) {
        $map_data = array(
            'address' => '',
            'lat' => floatval($latitude),
            'lng' => floatval($longitude),
            'zoom' => 15
        );
        
        update_field('map_location', $map_data, $post_id);
    }
    
    // If we have map location but no coordinates, update coordinates
    elseif ($map_location && (!$latitude || !$longitude)) {
        if (isset($map_location['lat']) && isset($map_location['lng'])) {
            update_field('latitude', $map_location['lat'], $post_id);
            update_field('longitude', $map_location['lng'], $post_id);
        }
    }
}
// Only add ACF save hook if ACF/SCF is active
if (function_exists('get_field')) {
    add_action('acf/save_post', 'intercity_sync_station_map_coordinates', 10);
}

/**
 * Add admin notice for Station mapping functionality
 */
function intercity_station_map_admin_notice() {
    global $post_type;
    
    if ($post_type !== 'station') {
        return;
    }
    
    ?>
    <div class="notice notice-info is-dismissible">
        <h3>🗺️ Station Location Guide</h3>
        <p><strong>How to set station location:</strong></p>
        <ol>
            <li><strong>Use Google Map:</strong> Click on the map to place a marker - coordinates will auto-update</li>
            <li><strong>Enter Coordinates:</strong> Manually enter latitude/longitude - map will auto-update</li>
            <li><strong>Search Location:</strong> Use the search box in the Google Map to find the station</li>
        </ol>
        <p><strong>📍 Tip:</strong> The map is centered on Zambia. Zoom in to find the exact station location for better accuracy.</p>
    </div>
    <?php
}
add_action('admin_notices', 'intercity_station_map_admin_notice');

/**
 * Get station location data for frontend use
 */
function intercity_get_station_location($station_id) {
    $latitude = get_field('latitude', $station_id);
    $longitude = get_field('longitude', $station_id);
    $map_location = get_field('map_location', $station_id);
    $address = get_field('station_address', $station_id);
    
    $location_data = array(
        'has_location' => false,
        'latitude' => null,
        'longitude' => null,
        'address' => $address,
        'google_maps_url' => '',
        'map_embed_url' => '',
        'coordinates_string' => ''
    );
    
    // Use map location if available, otherwise use individual coordinates
    if ($map_location && isset($map_location['lat']) && isset($map_location['lng'])) {
        $location_data['has_location'] = true;
        $location_data['latitude'] = $map_location['lat'];
        $location_data['longitude'] = $map_location['lng'];
    } elseif ($latitude && $longitude) {
        $location_data['has_location'] = true;
        $location_data['latitude'] = floatval($latitude);
        $location_data['longitude'] = floatval($longitude);
    }
    
    // Generate URLs and strings if we have coordinates
    if ($location_data['has_location']) {
        $lat = $location_data['latitude'];
        $lng = $location_data['longitude'];
        
        // Google Maps URL for directions
        $location_data['google_maps_url'] = "https://www.google.com/maps/dir/?api=1&destination={$lat},{$lng}";
        
        // Google Maps embed URL
        $location_data['map_embed_url'] = "https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q={$lat},{$lng}";
        
        // Coordinates as string
        $location_data['coordinates_string'] = number_format($lat, 6) . ', ' . number_format($lng, 6);
    }
    
    return $location_data;
}

/**
 * Display station location information
 */
function intercity_display_station_location($station_id, $show_map = false) {
    $location = intercity_get_station_location($station_id);
    
    if (!$location['has_location']) {
        return '<p class="no-location">Location information not available</p>';
    }
    
    $html = '<div class="station-location">';
    
    // Address
    if ($location['address']) {
        $html .= '<p class="station-address"><strong>Address:</strong> ' . esc_html($location['address']) . '</p>';
    }
    
    // Coordinates
    $html .= '<p class="station-coordinates"><strong>Coordinates:</strong> ' . esc_html($location['coordinates_string']) . '</p>';
    
    // Google Maps link
    $html .= '<p class="station-maps-link">';
    $html .= '<a href="' . esc_url($location['google_maps_url']) . '" target="_blank" rel="noopener">';
    $html .= 'Get Directions in Google Maps';
    $html .= '</a>';
    $html .= '</p>';
    
    // Embed map if requested
    if ($show_map && $location['has_location']) {
        $lat = $location['latitude'];
        $lng = $location['longitude'];
        
        $html .= '<div class="station-map-container">';
        $html .= '<div class="station-mini-map" id="station-map-' . $station_id . '">';
        $html .= '<p>Loading map...</p>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Add JavaScript for map
        $html .= '<script>
        if (typeof google !== "undefined") {
            const map = new google.maps.Map(document.getElementById("station-map-' . $station_id . '"), {
                zoom: 15,
                center: { lat: ' . $lat . ', lng: ' . $lng . ' }
            });
            
            new google.maps.Marker({
                position: { lat: ' . $lat . ', lng: ' . $lng . ' },
                map: map,
                title: "' . esc_js(get_the_title($station_id)) . '"
            });
        }
        </script>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * AJAX handler to get bus capacity
 */
function intercity_get_bus_capacity() {
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'get_bus_capacity')) {
        wp_die('Security check failed');
    }
    
    $bus_id = intval($_POST['bus_id']);
    
    if (!$bus_id) {
        wp_send_json_error('Invalid bus ID');
    }
    
    $capacity = get_field('seating_capacity', $bus_id);
    
    if (!$capacity) {
        wp_send_json_error('Bus capacity not found');
    }
    
    wp_send_json_success(array(
        'capacity' => $capacity,
        'bus_id' => $bus_id
    ));
}
add_action('wp_ajax_get_bus_capacity', 'intercity_get_bus_capacity');

/**
 * Get page image for current page/post
 */
function intercity_get_page_image($post_id = null) {
    // Don't show on front page
    if (is_front_page()) {
        return null;
    }

    if (!$post_id) {
        $post_id = get_the_ID();
    }

    if (!$post_id) {
        return null;
    }

    // Check if get_field function is available (ACF/SCF compatibility)
    if (function_exists('get_field')) {
        $page_image = get_field('page_image', $post_id);

        if (!empty($page_image) && is_array($page_image)) {
            return $page_image;
        }
    }

    // Try direct meta access as fallback
    $meta_image = get_post_meta($post_id, 'page_image', true);

    if (!empty($meta_image)) {
        // If it's a numeric ID, convert to attachment array
        if (is_numeric($meta_image)) {
            $attachment_url = wp_get_attachment_url($meta_image);
            if ($attachment_url) {
                return array(
                    'id' => $meta_image,
                    'url' => $attachment_url,
                    'alt' => get_post_meta($meta_image, '_wp_attachment_image_alt', true)
                );
            }
        } elseif (is_array($meta_image)) {
            return $meta_image;
        }
    }

    return null;
}

/**
 * Display page image if available
 */
function intercity_display_page_image($post_id = null, $size = 'page-image', $echo = true) {
    $page_image = intercity_get_page_image($post_id);
    
    if (!$page_image) {
        return '';
    }
    
    // Get the specified size or fallback to original
    $image_url = isset($page_image['sizes'][$size]) ? $page_image['sizes'][$size] : $page_image['url'];
    $image_width = isset($page_image['sizes'][$size . '-width']) ? $page_image['sizes'][$size . '-width'] : $page_image['width'];
    $image_height = isset($page_image['sizes'][$size . '-height']) ? $page_image['sizes'][$size . '-height'] : $page_image['height'];
    
    $html = '<div class="page-image-container">';
    $html .= '<img src="' . esc_url($image_url) . '" ';
    $html .= 'alt="' . esc_attr($page_image['alt']) . '" ';
    $html .= 'width="' . esc_attr($image_width) . '" ';
    $html .= 'height="' . esc_attr($image_height) . '" ';
    $html .= 'class="page-image" ';
    $html .= 'loading="lazy" ';
    $html .= '/>';
    $html .= '</div>';
    
    if ($echo) {
        echo $html;
    }
    
    return $html;
}

/**
 * Check if current page has page image
 */
function intercity_has_page_image($post_id = null) {
    return intercity_get_page_image($post_id) !== null;
}

/**
 * Get page image URL
 */
function intercity_get_page_image_url($post_id = null, $size = 'page-image') {
    $page_image = intercity_get_page_image($post_id);
    
    if (!$page_image) {
        return '';
    }
    
    // Get the specified size or fallback to original
    return isset($page_image['sizes'][$size]) ? $page_image['sizes'][$size] : $page_image['url'];
}

/**
 * Add body classes for page image
 */
function intercity_page_image_body_classes($classes) {
    if (intercity_has_page_image()) {
        $classes[] = 'has-page-image';
    } else {
        $classes[] = 'no-page-image';
    }
    
    return $classes;
}
add_filter('body_class', 'intercity_page_image_body_classes');