<?php
/**
 * DEBUG: Test meta field updates on pages
 */

// Load WordPress
define('WP_USE_THEMES', false);
require_once('../../../wp-load.php');

echo "<h1>Page Meta Debug Test</h1>";

// Get a page to test with
$pages = get_posts(array(
    'post_type' => 'page',
    'posts_per_page' => 1,
    'post_status' => 'any'
));

if (empty($pages)) {
    echo "<p style='color: red;'>❌ No pages found to test with</p>";
    exit;
}

$page = $pages[0];
echo "<h2>Testing with Page: {$page->post_title} (ID: {$page->ID})</h2>";

// Test 1: Try to update a simple meta field
echo "<h3>Test 1: Update simple meta field</h3>";
$result1 = update_post_meta($page->ID, 'test_meta_field', 'test_value');
if ($result1) {
    echo "<p style='color: green;'>✅ Simple meta update worked</p>";
} else {
    echo "<p style='color: red;'>❌ Simple meta update failed</p>";
}

// Test 2: Try to update _acf_changed specifically
echo "<h3>Test 2: Update _acf_changed meta field</h3>";
$result2 = update_post_meta($page->ID, '_acf_changed', 1);
if ($result2) {
    echo "<p style='color: green;'>✅ _acf_changed update worked</p>";
} else {
    echo "<p style='color: red;'>❌ _acf_changed update failed</p>";
}

// Test 3: Check current meta values
echo "<h3>Test 3: Current meta values</h3>";
$all_meta = get_post_meta($page->ID);
echo "<pre>";
print_r($all_meta);
echo "</pre>";

// Test 4: Check database errors
global $wpdb;
echo "<h3>Test 4: Database Status</h3>";
if ($wpdb->last_error) {
    echo "<p style='color: red;'>❌ Database Error: " . $wpdb->last_error . "</p>";
} else {
    echo "<p style='color: green;'>✅ No database errors</p>";
}

// Test 5: Test with a different post type
echo "<h3>Test 5: Compare with other post type</h3>";
$other_posts = get_posts(array(
    'post_type' => 'post',
    'posts_per_page' => 1,
    'post_status' => 'any'
));

if (!empty($other_posts)) {
    $other_post = $other_posts[0];
    $result5 = update_post_meta($other_post->ID, '_acf_changed', 1);
    if ($result5) {
        echo "<p style='color: green;'>✅ _acf_changed works on post type 'post'</p>";
    } else {
        echo "<p style='color: red;'>❌ _acf_changed fails on post type 'post' too</p>";
    }
} else {
    echo "<p>No posts found to compare with</p>";
}