<?php
/**
 * SCF (Secure Custom Field) Fields for Intercity Zambia
 */

/**
 * Custom validation for minimum character count
 */
function intercity_validate_min_chars($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    if (empty(trim($value)) && !$field['required']) {
        return $valid;
    }

    if (isset($field['min']) && strlen(trim($value)) < $field['min']) {
        return sprintf('The %s field must be at least %d characters', $field['label'], $field['min']);
    }

    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// Only add ACF validation filters if ACF is active
// if (function_exists('acf_get_field')) {
//     add_filter('acf/validate_value/type=textarea', 'intercity_validate_min_chars', 10, 4);
//     add_filter('acf/validate_value/type=text', 'intercity_validate_min_chars', 10, 4);
//     add_filter('acf/validate_value/type=url', 'intercity_validate_min_chars', 10, 4);
// }

/**
 * Custom validation for maximum character count
 */
function intercity_validate_max_chars($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    if (empty(trim($value)) && !$field['required']) {
        return $valid;
    }

    if (isset($field['max']) && strlen(trim($value)) > $field['max']) {
        return sprintf('The %s field must not exceed %d characters', $field['label'], $field['max']);
    }

    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// Only add ACF validation filters if ACF is active
// if (function_exists('acf_get_field')) {
//     add_filter('acf/validate_value/type=textarea', 'intercity_validate_max_chars', 10, 4);
//     add_filter('acf/validate_value/type=text', 'intercity_validate_max_chars', 10, 4);
//     add_filter('acf/validate_value/type=url', 'intercity_validate_max_chars', 10, 4);
// }

/**
 * Custom validation for future dates
 */
function intercity_validate_future_date($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    // Skip validation for service_date field (it has its own validation)
    if ($field['name'] === 'service_date') {
        return $valid;
    }
    
    if (empty($value) && !$field['required']) {
        return $valid;
    }
    
    $selected_date = strtotime($value);
    $today = strtotime(date('Y-m-d'));
    
    if ($selected_date <= $today) {
        return 'Please select a future date (tomorrow or later) for the departure.';
    }
    
    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// add_filter('acf/validate_value/type=date_picker', 'intercity_validate_future_date', 10, 4);

/**
 * Custom validation for service date (future date)
 */
function intercity_validate_service_date($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    // Only validate if this is the service date field
    if ($field['name'] !== 'service_date') {
        return $valid;
    }
    
    if (empty($value) && !$field['required']) {
        return $valid;
    }
    
    $selected_date = strtotime($value);
    $today = strtotime(date('Y-m-d'));
    
    if ($selected_date < $today) {
        return 'Please select today or a future date for the service.';
    }
    
    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// add_filter('acf/validate_value/name=service_date', 'intercity_validate_service_date', 10, 4);

/**
 * Custom validation for tracking times
 */
function intercity_validate_tracking_times($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    if (empty($value) && !$field['required']) {
        return $valid;
    }
    
    // Validate that departure time is after arrival time for tracking points
    if ($field['name'] === 'departure_time' && !empty($value)) {
        $arrival_time = $input['arrival_time'] ?? '';
        
        if (!empty($arrival_time) && strtotime($value) <= strtotime($arrival_time)) {
            return 'Departure time must be after arrival time.';
        }
    }
    
    // Validate that end time is after start time for overall tracking
    if ($field['name'] === 'tracking_end_time' && !empty($value)) {
        $start_time = $input['tracking_start_time'] ?? '';
        
        if (!empty($start_time) && strtotime($value) <= strtotime($start_time)) {
            return 'End time must be after start time.';
        }
    }
    
    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// add_filter('acf/validate_value/name=departure_time', 'intercity_validate_tracking_times', 10, 4);
// add_filter('acf/validate_value/name=tracking_end_time', 'intercity_validate_tracking_times', 10, 4);

/**
 * Custom validation for image URL field
 */
function intercity_validate_image_url($valid, $value, $field, $input) {
    if ($valid !== true) {
        return $valid;
    }
    
    // Skip validation if field is empty (it's optional)
    if (empty(trim($value))) {
        return $valid;
    }
    
    // Only validate if this is the image_url field
    if ($field['name'] !== 'image_url') {
        return $valid;
    }
    
    // Check if it starts with forward slash (PHP 8+ function with fallback)
    $trimmed_value = trim($value);
    if (function_exists('str_starts_with')) {
        $starts_with_slash = str_starts_with($trimmed_value, '/');
    } else {
        $starts_with_slash = substr($trimmed_value, 0, 1) === '/';
    }
    
    if (!$starts_with_slash) {
        return 'Image URL must start with a forward slash (e.g., /about/about-us)';
    }
    
    // Check for invalid characters (basic validation)
    if (preg_match('/[^a-zA-Z0-9\/\-_]/', trim($value))) {
        return 'Image URL contains invalid characters. Use only letters, numbers, hyphens, underscores, and forward slashes.';
    }
    
    return $valid;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// add_filter('acf/validate_value/name=image_url', 'intercity_validate_image_url', 10, 4);

/**
 * Auto-populate total_seats and bus_operator when bus is selected for a service
 */
function intercity_auto_populate_service_capacity($post_id) {
    // Check if ACF is active
    if (!function_exists('get_field') || !function_exists('update_field')) {
        return;
    }
    
    // Only run for service post type
    if (!$post_id || !is_numeric($post_id) || get_post_type($post_id) !== 'service') {
        return;
    }
    
    $bus_id = get_field('service_bus', $post_id);
    
    if ($bus_id && is_numeric($bus_id)) {
        // Auto-populate bus capacity
        $bus_capacity = get_field('seating_capacity', $bus_id);
        
        if ($bus_capacity && is_numeric($bus_capacity)) {
            // Update total_seats with bus capacity
            update_field('total_seats', intval($bus_capacity), $post_id);
            
            // Calculate available seats (total - booked)
            $booked_seats = get_field('booked_seats', $post_id);
            $booked_seats = $booked_seats && is_numeric($booked_seats) ? intval($booked_seats) : 0;
            $available_seats = intval($bus_capacity) - $booked_seats;
            
            // Update available_seats
            update_field('available_seats', max(0, $available_seats), $post_id);
        }
        
        // Auto-populate bus operator
        $bus_operator = get_field('operator', $bus_id);
        if ($bus_operator && is_object($bus_operator) && isset($bus_operator->ID) && is_numeric($bus_operator->ID)) {
            update_field('service_bus_operator', intval($bus_operator->ID), $post_id);
        }
    }
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// Only add ACF hooks if ACF is active
// if (function_exists('get_field')) {
//     add_action('acf/save_post', 'intercity_auto_populate_service_capacity', 20);
// }

/**
 * Register SCF fields
 */
function intercity_register_scf_fields() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    // Province Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_province',
        'title' => 'Province Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'province',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_province_name',
                'label' => 'Province Name',
                'name' => 'province_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Official name of the province (2-50 characters)',
                'min' => 2,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_province_active',
                'label' => 'Active',
                'name' => 'province_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this province active for operations?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_province_description',
                'label' => 'Province Description',
                'name' => 'province_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Brief description of the province (optional)',
                'max' => 300,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // City Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_city',
        'title' => 'City Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'city',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_city_name',
                'label' => 'City Name',
                'name' => 'city_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Official name of the city (2-50 characters)',
                'min' => 2,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_city_province',
                'label' => 'Province',
                'name' => 'province',
                'type' => 'post_object',
                'post_type' => array('province'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the province this city belongs to',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_city_active',
                'label' => 'Active',
                'name' => 'city_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this city active for operations?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_city_description',
                'label' => 'City Description',
                'name' => 'city_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Brief description of the city (optional)',
                'max' => 300,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Station Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_station',
        'title' => 'Station Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'station',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_station_name',
                'label' => 'Station Name',
                'name' => 'station_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Official name of the station (2-50 characters)',
                'min' => 2,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_city',
                'label' => 'City',
                'name' => 'city',
                'type' => 'post_object',
                'post_type' => array('city'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the city this station belongs to',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_type',
                'label' => 'Station Type',
                'name' => 'station_type',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'main_terminal' => 'Main Terminal',
                    'town_stop' => 'Town Stop',
                    'roadside_stop' => 'Roadside Stop',
                ),
                'default_value' => 'main_terminal',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_active',
                'label' => 'Active',
                'name' => 'station_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this station active for operations?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_address',
                'label' => 'Station Address',
                'name' => 'station_address',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Physical address of the station (optional)',
                'max' => 200,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_station_latitude',
                'label' => 'Latitude',
                'name' => 'latitude',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'GPS latitude coordinate for Google Maps',
                'step' => 0.000001,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_longitude',
                'label' => 'Longitude',
                'name' => 'longitude',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'GPS longitude coordinate for Google Maps',
                'step' => 0.000001,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_station_map_location',
                'label' => 'Map Location',
                'name' => 'map_location',
                'type' => 'google_map',
                'required' => 0,
                'instructions' => 'Click on the map to set the exact location of the station. This will also update the latitude and longitude fields above.',
                'center_lat' => -15.3875,
                'center_lng' => 28.3228,
                'zoom' => 6,
                'height' => 400,
                'wrapper' => array(
                    'width' => '100',
                    'class' => 'station-map-field',
                ),
            ),
            array(
                'key' => 'field_station_description',
                'label' => 'Station Description',
                'name' => 'station_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Additional information about the station (optional)',
                'max' => 500,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Bus Operator Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_bus_operator',
        'title' => 'Bus Operator Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'bus_operator',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_operator_name',
                'label' => 'Operator Name',
                'name' => 'operator_name',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Official name of the bus operator (2-50 characters)',
                'min' => 2,
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_operator_active',
                'label' => 'Active',
                'name' => 'operator_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this operator active?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_operator_logo',
                'label' => 'Operator Logo',
                'name' => 'operator_logo',
                'type' => 'image',
                'required' => 0,
                'instructions' => 'Upload operator logo image (optional)',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_head_office_location',
                'label' => 'Head Office Location',
                'name' => 'head_office_location',
                'type' => 'post_object',
                'post_type' => array('city'),
                'return_format' => 'object',
                'required' => 0,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'City where the head office is located (optional)',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_contact_number',
                'label' => 'Contact Number',
                'name' => 'contact_number',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Primary contact phone number (optional)',
                'max' => 20,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_contact_email',
                'label' => 'Contact Email',
                'name' => 'contact_email',
                'type' => 'email',
                'required' => 0,
                'instructions' => 'Primary contact email address (optional)',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_operator_description',
                'label' => 'Operator Description',
                'name' => 'operator_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Brief description of the operator (optional)',
                'max' => 300,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_operator_notes',
                'label' => 'Notes',
                'name' => 'operator_notes',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Additional notes about the operator (optional)',
                'max' => 500,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Route Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_route',
        'title' => 'Route Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'route',
                ),
            ),
        ),
        'fields' => array(
            // Route Name (Auto-generated)
            array(
                'key' => 'field_route_name',
                'label' => 'Route Name (Auto-generated)',
                'name' => 'route_name',
                'type' => 'text',
                'required' => 0,
                'readonly' => 1,
                'instructions' => 'Auto-generated from departure and arrival cities (e.g., "Lusaka - Livingstone")',
                'wrapper' => array(
                    'width' => '100',
                    'class' => 'route-name-field',
                ),
            ),
            
            // Departure City/Town
            array(
                'key' => 'field_departure_city',
                'label' => 'Departure City/Town',
                'name' => 'departure_city',
                'type' => 'post_object',
                'post_type' => array('city'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the departure city/town',
                'wrapper' => array(
                    'width' => '50',
                    'class' => 'departure-city-field',
                ),
            ),
            
            // Departure Station
            array(
                'key' => 'field_departure_station',
                'label' => 'Departure Station',
                'name' => 'departure_station',
                'type' => 'post_object',
                'post_type' => array('station'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'allow_null' => 1,
                'filters' => array(),
                'instructions' => 'Will auto-populate based on selected city',
                'wrapper' => array(
                    'width' => '50',
                    'class' => 'departure-station-field',
                ),
            ),
            
            // Arrival City/Town
            array(
                'key' => 'field_arrival_city',
                'label' => 'Arrival City/Town',
                'name' => 'arrival_city',
                'type' => 'post_object',
                'post_type' => array('city'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the arrival city/town',
                'wrapper' => array(
                    'width' => '50',
                    'class' => 'arrival-city-field',
                ),
            ),
            
            // Arrival Station
            array(
                'key' => 'field_arrival_station',
                'label' => 'Arrival Station',
                'name' => 'arrival_station',
                'type' => 'post_object',
                'post_type' => array('station'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'allow_null' => 1,
                'filters' => array(),
                'instructions' => 'Will auto-populate based on selected city',
                'wrapper' => array(
                    'width' => '50',
                    'class' => 'arrival-station-field',
                ),
            ),
            
            // Bus Operator
            array(
                'key' => 'field_route_operator',
                'label' => 'Bus Operator',
                'name' => 'operator',
                'type' => 'post_object',
                'post_type' => array('bus_operator'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the bus operator that owns this route',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            
            // Active Status
            array(
                'key' => 'field_route_active',
                'label' => 'Active Route',
                'name' => 'active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Toggle route availability in frontend',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            
            // Description
            array(
                'key' => 'field_route_description',
                'label' => 'Description',
                'name' => 'description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Optional description of the route',
                'max' => 500,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            
            // Distance
            array(
                'key' => 'field_distance_km',
                'label' => 'Distance (km)',
                'name' => 'distance_km',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Total distance in kilometers (optional)',
                'step' => 0.1,
                'min' => 0,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            
            // Estimated Duration
            array(
                'key' => 'field_estimated_duration',
                'label' => 'Estimated Duration',
                'name' => 'estimated_duration',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'e.g., "6h 30m" or "4 hours 15 minutes" (optional)',
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            
            // Priority
            array(
                'key' => 'field_route_priority',
                'label' => 'Priority',
                'name' => 'priority',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Higher numbers appear first in search results (0-100)',
                'default_value' => 0,
                'min' => 0,
                'max' => 100,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            
            // Internal Notes
            array(
                'key' => 'field_route_notes',
                'label' => 'Internal Notes',
                'name' => 'notes',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Internal admin notes (not shown on frontend)',
                'max' => 1000,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Route Stop Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_route_stop',
        'title' => 'Route Stop Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'route_stop',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_route_stop_route',
                'label' => 'Route',
                'name' => 'route',
                'type' => 'post_object',
                'post_type' => array('route'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the route this stop belongs to',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_stop_active',
                'label' => 'Active',
                'name' => 'stop_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this route stop active?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_from_station',
                'label' => 'From Station',
                'name' => 'from_station',
                'type' => 'post_object',
                'post_type' => array('station'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Starting station for this segment',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_to_station',
                'label' => 'To Station',
                'name' => 'to_station',
                'type' => 'post_object',
                'post_type' => array('station'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Ending station for this segment',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_fare',
                'label' => 'Fare (ZMW)',
                'name' => 'fare',
                'type' => 'number',
                'required' => 1,
                'instructions' => 'Fare for this segment in Zambian Kwacha',
                'step' => 0.01,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_segment_duration',
                'label' => 'Segment Duration',
                'name' => 'segment_duration',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'e.g., 1 hour 15 minutes (optional)',
                'max' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_segment_distance_km',
                'label' => 'Segment Distance (KM)',
                'name' => 'segment_distance_km',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Distance of this segment in kilometers (optional)',
                'step' => 0.1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_departure_time',
                'label' => 'Departure Time',
                'name' => 'departure_time',
                'type' => 'time_picker',
                'required' => 0,
                'instructions' => 'Scheduled departure time from this station (optional)',
                'display_format' => 'g:i a',
                'return_format' => 'g:i a',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_arrival_time',
                'label' => 'Arrival Time',
                'name' => 'arrival_time',
                'type' => 'time_picker',
                'required' => 0,
                'instructions' => 'Estimated arrival time at this station (optional)',
                'display_format' => 'g:i a',
                'return_format' => 'g:i a',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),

            array(
                'key' => 'field_stop_description',
                'label' => 'Description',
                'name' => 'stop_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Additional information about this stop (optional)',
                'max' => 300,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Bus Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_bus',
        'title' => 'Bus Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'bus',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_bus_title_help',
                'label' => 'Bus Title Instructions',
                'name' => 'bus_title_help',
                'type' => 'message',
                'message' => '<strong>📝 Important:</strong> Please enter a descriptive title for this bus in the main title field above. Examples:
                <ul>
                    <li>"Mubanga Express - ACG 1234" (Operator + Plate Number)</li>
                    <li>"Luxury Coach #3 - ABC 5678" (Type + Number + Plate)</li>
                    <li>"Standard Bus - XYZ 9012" (Type + Plate)</li>
                </ul>
                This title will be used to identify the bus in service assignments and dropdown lists.',
                'new_lines' => 'wpautop',
                'esc_html' => 0,
                'wrapper' => array(
                    'width' => '100',
                    'class' => 'bus-title-help',
                ),
            ),
            array(
                'key' => 'field_plate_number',
                'label' => 'Plate Number',
                'name' => 'plate_number',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Vehicle registration plate number (5-15 characters)',
                'min' => 5,
                'max' => 15,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_bus_operator',
                'label' => 'Bus Operator',
                'name' => 'operator',
                'type' => 'post_object',
                'post_type' => array('bus_operator'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the bus operator',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_seating_capacity',
                'label' => 'Seating Capacity',
                'name' => 'seating_capacity',
                'type' => 'number',
                'required' => 1,
                'instructions' => 'Total number of seats',
                'step' => 1,
                'min' => 1,
                'max' => 100,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_bus_type',
                'label' => 'Bus Type',
                'name' => 'bus_type',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'standard' => 'Standard Bus',
                    'luxury' => 'Luxury Bus',
                    'minibus' => 'Minibus',
                    'coach' => 'Coach',
                ),
                'default_value' => 'standard',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_bus_condition',
                'label' => 'Bus Condition',
                'name' => 'bus_condition',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'excellent' => 'Excellent',
                    'good' => 'Good',
                    'fair' => 'Fair',
                    'needs_maintenance' => 'Needs Maintenance',
                ),
                'default_value' => 'good',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_bus_active',
                'label' => 'Active',
                'name' => 'bus_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this bus active for operations?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_assigned_routes',
                'label' => 'Assigned Routes',
                'name' => 'assigned_routes',
                'type' => 'post_object',
                'post_type' => array('route'),
                'return_format' => 'object',
                'required' => 0,
                'multiple' => 1,
                'ui' => 1,
                'instructions' => 'Select routes this bus operates on (optional)',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_amenities',
                'label' => 'Amenities',
                'name' => 'amenities',
                'type' => 'checkbox',
                'required' => 0,
                'instructions' => 'Select available amenities (optional)',
                'choices' => array(
                    'air_conditioning' => 'Air Conditioning',
                    'wifi' => 'WiFi',
                    'usb_charging' => 'USB Charging',
                    'entertainment' => 'Entertainment System',
                    'restroom' => 'Restroom',
                    'refreshments' => 'Refreshments',
                    'reclining_seats' => 'Reclining Seats',
                    'reading_lights' => 'Reading Lights',
                ),
                'allow_custom' => 0,
                'save_custom' => 0,
                'toggle' => 0,
                'return_format' => 'value',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_bus_notes',
                'label' => 'Notes',
                'name' => 'bus_notes',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Additional notes about this bus (optional)',
                'max' => 500,
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Service Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_service',
        'title' => 'Service Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'service',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_service_name',
                'label' => 'Service Name',
                'name' => 'service_name',
                'type' => 'post_object',
                'post_type' => array('service_name'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select a standardized service name from the predefined list',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_date',
                'label' => 'Service Date',
                'name' => 'service_date',
                'type' => 'date_picker',
                'required' => 1,
                'instructions' => 'Date of the service',
                'display_format' => 'd/m/Y',
                'return_format' => 'Y-m-d',
                'first_day' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_departure_time',
                'label' => 'Departure Time',
                'name' => 'departure_time',
                'type' => 'time_picker',
                'required' => 1,
                'instructions' => 'Scheduled departure time',
                'display_format' => 'H:i',
                'return_format' => 'H:i',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_route',
                'label' => 'Route',
                'name' => 'service_route',
                'type' => 'post_object',
                'post_type' => array('route'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the route for this service',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_bus_operator',
                'label' => 'Bus Operator',
                'name' => 'service_bus_operator',
                'type' => 'post_object',
                'post_type' => array('bus_operator'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the bus operator for this service',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_bus_operator',
                'label' => 'Bus Operator',
                'name' => 'service_bus_operator',
                'type' => 'post_object',
                'post_type' => array('bus_operator'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the bus operator for this service',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_bus',
                'label' => 'Bus',
                'name' => 'service_bus',
                'type' => 'post_object',
                'post_type' => array('bus'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the bus assigned to this service',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_description',
                'label' => 'Service Description',
                'name' => 'service_description',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Brief description of the service for admins and users (optional)',
                'max' => 500,
                'rows' => 3,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_service_status',
                'label' => 'Service Status',
                'name' => 'service_status',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'scheduled' => 'Scheduled',
                    'departed' => 'Departed',
                    'completed' => 'Completed',
                    'cancelled' => 'Cancelled',
                ),
                'default_value' => 'scheduled',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_total_seats',
                'label' => 'Total Seats',
                'name' => 'total_seats',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Total seating capacity (automatically inherited from selected bus)',
                'step' => 1,
                'min' => 1,
                'readonly' => 1,
                'wrapper' => array(
                    'width' => '33',
                ),
            ),
            array(
                'key' => 'field_booked_seats',
                'label' => 'Booked Seats',
                'name' => 'booked_seats',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Number of seats already booked (auto-calculated)',
                'step' => 1,
                'min' => 0,
                'default_value' => 0,
                'readonly' => 1,
                'wrapper' => array(
                    'width' => '33',
                ),
            ),
            array(
                'key' => 'field_available_seats',
                'label' => 'Available Seats',
                'name' => 'available_seats',
                'type' => 'number',
                'required' => 0,
                'instructions' => 'Available seats for booking (automatically calculated: Total - Booked)',
                'step' => 1,
                'min' => 0,
                'readonly' => 1,
                'wrapper' => array(
                    'width' => '34',
                ),
            ),
            array(
                'key' => 'field_service_active',
                'label' => 'Active',
                'name' => 'service_active',
                'type' => 'true_false',
                'required' => 0,
                'instructions' => 'Is this service active for bookings?',
                'default_value' => 1,
                'ui' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));

    // Tracking Post Type Fields
    acf_add_local_field_group(array(
        'key' => 'group_tracking',
        'title' => 'Tracking Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'tracking',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_tracking_service',
                'label' => 'Service',
                'name' => 'tracking_service',
                'type' => 'post_object',
                'post_type' => array('service'),
                'return_format' => 'object',
                'required' => 1,
                'multiple' => 0,
                'ui' => 1,
                'instructions' => 'Select the service to track',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_tracking_status',
                'label' => 'Overall Tracking Status',
                'name' => 'tracking_status',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'not_started' => 'Not Started',
                    'in_progress' => 'In Progress',
                    'completed' => 'Completed',
                    'delayed' => 'Delayed',
                    'cancelled' => 'Cancelled',
                ),
                'default_value' => 'not_started',
                'instructions' => 'Overall status of the tracking session',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_tracking_start_time',
                'label' => 'Actual Start Time',
                'name' => 'tracking_start_time',
                'type' => 'date_time_picker',
                'required' => 0,
                'instructions' => 'When the service actually started (may differ from scheduled time)',
                'display_format' => 'd/m/Y H:i',
                'return_format' => 'Y-m-d H:i:s',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_tracking_end_time',
                'label' => 'Actual End Time',
                'name' => 'tracking_end_time',
                'type' => 'date_time_picker',
                'required' => 0,
                'instructions' => 'When the service actually ended',
                'display_format' => 'd/m/Y H:i',
                'return_format' => 'Y-m-d H:i:s',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_tracking_points',
                'label' => 'Tracking Points',
                'name' => 'tracking_points',
                'type' => 'repeater',
                'required' => 0,
                'instructions' => 'Add tracking points for each stop along the route',
                'collapsed' => 'field_tracking_point_stop',
                'min' => 0,
                'max' => 0,
                'layout' => 'table',
                'button_label' => 'Add Tracking Point',
                'sub_fields' => array(
                    array(
                        'key' => 'field_tracking_point_stop',
                        'label' => 'Stop (City/Town)',
                        'name' => 'stop',
                        'type' => 'post_object',
                        'post_type' => array('city'),
                        'return_format' => 'object',
                        'required' => 1,
                        'multiple' => 0,
                        'ui' => 1,
                        'instructions' => 'Select the city/town stop',
                        'wrapper' => array(
                            'width' => '18',
                        ),
                    ),
                    array(
                        'key' => 'field_tracking_point_status',
                        'label' => 'Status',
                        'name' => 'status',
                        'type' => 'select',
                        'required' => 1,
                        'choices' => array(
                            'not_visited' => 'Yet to Arrive',
                            'in_transit' => 'On the Way',
                            'arrived' => 'Arrived at Stop',
                            'departed' => 'Departed',
                        ),
                        'default_value' => 'not_visited',
                        'instructions' => 'Current status at this stop',
                        'wrapper' => array(
                            'width' => '16',
                        ),
                    ),
                    array(
                        'key' => 'field_tracking_point_arrival_time',
                        'label' => 'Arrival Time',
                        'name' => 'arrival_time',
                        'type' => 'date_time_picker',
                        'required' => 0,
                        'instructions' => 'When the bus arrived at this stop',
                        'display_format' => 'd/m/Y H:i',
                        'return_format' => 'Y-m-d H:i:s',
                        'wrapper' => array(
                            'width' => '20',
                        ),
                    ),
                    array(
                        'key' => 'field_tracking_point_departure_time',
                        'label' => 'Departure Time',
                        'name' => 'departure_time',
                        'type' => 'date_time_picker',
                        'required' => 0,
                        'instructions' => 'When the bus departed from this stop',
                        'display_format' => 'd/m/Y H:i',
                        'return_format' => 'Y-m-d H:i:s',
                        'wrapper' => array(
                            'width' => '20',
                        ),
                    ),
                    array(
                        'key' => 'field_tracking_point_notes',
                        'label' => 'Notes',
                        'name' => 'notes',
                        'type' => 'textarea',
                        'required' => 0,
                        'instructions' => 'Additional notes about this stop (delays, issues, etc.)',
                        'rows' => 2,
                        'wrapper' => array(
                            'width' => '46',
                        ),
                    ),
                ),
            ),
            array(
                'key' => 'field_tracking_notes',
                'label' => 'Overall Tracking Notes',
                'name' => 'tracking_notes',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'General notes about the entire tracking session',
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Service Name Field Group
    acf_add_local_field_group(array(
        'key' => 'group_service_name',
        'title' => 'Service Name Details',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'service_name',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_service_name_value',
                'label' => 'Service Name',
                'name' => 'service_name_value',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Enter the standardized service name or code (e.g., "675", "06:00 - Lusaka to Livingstone")',
                'min' => 3,
                'max' => 100,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_service_name_notes',
                'label' => 'Notes',
                'name' => 'service_name_notes',
                'type' => 'textarea',
                'required' => 0,
                'instructions' => 'Optional notes about this service name (e.g., "Our 6 hours service restricted to the Livingstone and Lusaka routes", route details, frequency, etc.)',
                'rows' => 4,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
        ),
    ));

    // Page Image Field Group (for all pages except front page and all custom post types)
    acf_add_local_field_group(array(
        'key' => 'group_page_image',
        'title' => 'Page Image',
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'page',
                ),
                array(
                    'param' => 'page_type',
                    'operator' => '!=',
                    'value' => 'front_page',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'province',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'city',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'station',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'bus_operator',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'bus',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'route',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'route_stop',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'service',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'service_name',
                ),
            ),
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'tracking',
                ),
            ),
        ),
        'fields' => array(
            array(
                'key' => 'field_page_image',
                'label' => 'Page Image',
                'name' => 'page_image',
                'type' => 'image',
                'required' => 0,
                'instructions' => 'Optional image to display at the top of the page. If not provided, the page content will occupy the full width. Recommended size: 1280x500px',
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_image_description',
                'label' => 'Image Description',
                'name' => 'image_description',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Optional short description for the page image (5-40 characters)',
                'min' => 5,
                'max' => 40,
                'placeholder' => 'e.g., "Explore our services" or "Learn more about us"',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_image_url',
                'label' => 'Image URL',
                'name' => 'image_url',
                'type' => 'text',
                'required' => 0,
                'instructions' => 'Optional URL slug to link the image to (starting with forward slash, e.g., /about/about-us)',
                'placeholder' => '/about/about-us',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
    ));
}
// Re-enable field registration (needed for fields to display)
// Only add ACF init hook if ACF/SCF is active
if (function_exists('get_field')) {
    add_action('acf/init', 'intercity_register_scf_fields');
}