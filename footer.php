<?php
/**
 * Footer Template
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */
?>

    </div><!-- #content -->

    <!-- Footer -->
    <footer id="colophon" class="site-footer bg-neutral-800 text-white">
        <!-- Main Footer -->
        <div class="py-16">
            <div class="container-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Company Info -->
                    <div class="lg:col-span-1">
                        <div class="mb-6">
                            <?php if (get_theme_mod('intercity_logo')) : ?>
                                <img src="<?php echo esc_url(get_theme_mod('intercity_logo')); ?>" alt="<?php bloginfo('name'); ?>" class="h-12 w-auto mb-4 brightness-0 invert">
                            <?php else : ?>
                                <img src="<?php echo INTERCITY_THEME_URI; ?>/assets/img/intercityzambia.svg" alt="<?php bloginfo('name'); ?>" class="h-12 w-auto mb-4 brightness-0 invert">
                            <?php endif; ?>
                        </div>
                        
                        <p class="text-neutral-300 mb-6 leading-relaxed">
                            <?php _e('Connecting Zambia through reliable bus transportation. Book your journey with trusted operators across the country.', 'intercityzambia'); ?>
                        </p>
                        
                        <!-- Social Links -->
                        <div class="flex space-x-4">
                            <a href="#" class="w-10 h-10 bg-neutral-700 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors" aria-label="<?php _e('Facebook', 'intercityzambia'); ?>">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <a href="#" class="w-10 h-10 bg-neutral-700 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors" aria-label="<?php _e('Twitter', 'intercityzambia'); ?>">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                            <a href="#" class="w-10 h-10 bg-neutral-700 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors" aria-label="<?php _e('WhatsApp', 'intercityzambia'); ?>">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                            </a>
                            <a href="#" class="w-10 h-10 bg-neutral-700 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors" aria-label="<?php _e('Instagram', 'intercityzambia'); ?>">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h3 class="text-lg font-serif font-semibold mb-6">
                            <?php _e('Quick Links', 'intercityzambia'); ?>
                        </h3>
                        <ul class="space-y-3">
                            <li>
                                <a href="<?php echo home_url(); ?>" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Home', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Search Routes', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Bus Operators', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo get_permalink(get_option('page_for_posts')); ?>" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Travel News', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('About Us', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Contact', 'intercityzambia'); ?>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Popular Routes -->
                    <div>
                        <h3 class="text-lg font-serif font-semibold mb-6">
                            <?php _e('Popular Routes', 'intercityzambia'); ?>
                        </h3>
                        <ul class="space-y-3">
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Lusaka to Kitwe', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Ndola to Lusaka', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Lusaka to Livingstone', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Kitwe to Chipata', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Kabwe to Kasama', 'intercityzambia'); ?>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="text-neutral-300 hover:text-primary-400 transition-colors">
                                    <?php _e('Solwezi to Lusaka', 'intercityzambia'); ?>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Contact & Newsletter -->
                    <div>
                        <h3 class="text-lg font-serif font-semibold mb-6">
                            <?php _e('Stay Connected', 'intercityzambia'); ?>
                        </h3>
                        
                        <!-- Contact Info -->
                        <div class="space-y-3 mb-6">
                            <?php if (get_theme_mod('contact_phone')) : ?>
                                <div class="flex items-center text-neutral-300">
                                    <svg class="w-5 h-5 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <a href="tel:<?php echo esc_attr(get_theme_mod('contact_phone')); ?>" class="hover:text-primary-400 transition-colors">
                                        <?php echo esc_html(get_theme_mod('contact_phone')); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_theme_mod('contact_email')) : ?>
                                <div class="flex items-center text-neutral-300">
                                    <svg class="w-5 h-5 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <a href="mailto:<?php echo esc_attr(get_theme_mod('contact_email')); ?>" class="hover:text-primary-400 transition-colors">
                                        <?php echo esc_html(get_theme_mod('contact_email')); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center text-neutral-300">
                                <svg class="w-5 h-5 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span><?php _e('Lusaka, Zambia', 'intercityzambia'); ?></span>
                            </div>
                        </div>

                        <!-- Newsletter Signup -->
                        <div>
                            <h4 class="font-semibold mb-3"><?php _e('Newsletter', 'intercityzambia'); ?></h4>
                            <p class="text-neutral-400 text-sm mb-4">
                                <?php _e('Subscribe to get travel updates and special offers.', 'intercityzambia'); ?>
                            </p>
                            <form class="space-y-3" x-data="newsletter()">
                                <div class="flex">
                                    <input type="email" x-model="email" placeholder="<?php _e('Your email', 'intercityzambia'); ?>" class="flex-1 px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-l-lg text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <button type="submit" @click="subscribe()" class="px-4 py-2 bg-primary-600 text-white rounded-r-lg hover:bg-primary-700 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div x-show="message" x-text="message" class="text-sm" :class="success ? 'text-green-400' : 'text-red-400'"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-neutral-700 py-6">
            <div class="container-custom">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="text-neutral-400 text-sm mb-4 md:mb-0">
                        <p>
                            &copy; <?php echo date('Y'); ?> 
                            <a href="<?php echo home_url(); ?>" class="hover:text-primary-400 transition-colors">
                                <?php bloginfo('name'); ?>
                            </a>. 
                            <?php _e('All rights reserved.', 'intercityzambia'); ?>
                        </p>
                    </div>
                    
                    <div class="flex flex-wrap items-center space-x-6 text-sm">
                        <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">
                            <?php _e('Privacy Policy', 'intercityzambia'); ?>
                        </a>
                        <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">
                            <?php _e('Terms of Service', 'intercityzambia'); ?>
                        </a>
                        <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">
                            <?php _e('Cookie Policy', 'intercityzambia'); ?>
                        </a>
                        <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">
                            <?php _e('Help Center', 'intercityzambia'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

        </div><!-- #page -->
    </div><!-- #site-wrapper -->
</div><!-- Overall Site Container -->

<!-- Back to Top Button -->
<button id="back-to-top" class="fixed bottom-6 right-6 w-12 h-12 bg-primary-800 text-white rounded-full shadow-lg hover:bg-primary-900 transition-all duration-300 opacity-0 pointer-events-none z-50" onclick="scrollToTop()">
    <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<!-- Scripts -->
<script>
// Newsletter functionality
function newsletter() {
    return {
        email: '',
        message: '',
        success: false,
        
        async subscribe() {
            if (!this.email) {
                this.message = '<?php _e("Please enter your email address", "intercityzambia"); ?>';
                this.success = false;
                return;
            }
            
            if (!this.isValidEmail(this.email)) {
                this.message = '<?php _e("Please enter a valid email address", "intercityzambia"); ?>';
                this.success = false;
                return;
            }
            
            try {
                // Simulate API call - replace with actual implementation
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                this.message = '<?php _e("Thank you for subscribing!", "intercityzambia"); ?>';
                this.success = true;
                this.email = '';
                
                setTimeout(() => {
                    this.message = '';
                }, 3000);
            } catch (error) {
                this.message = '<?php _e("Something went wrong. Please try again.", "intercityzambia"); ?>';
                this.success = false;
            }
        },
        
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    }
}

// Back to top functionality
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Show/hide back to top button
window.addEventListener('scroll', function() {
    const backToTopButton = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTopButton.classList.remove('opacity-0', 'pointer-events-none');
        backToTopButton.classList.add('opacity-100');
    } else {
        backToTopButton.classList.add('opacity-0', 'pointer-events-none');
        backToTopButton.classList.remove('opacity-100');
    }
});

// Loading states for forms
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[data-loading]');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                submitButton.disabled = true;
                
                setTimeout(() => {
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    submitButton.disabled = false;
                }, 2000);
            }
        });
    });
});
</script>

<?php wp_footer(); ?>

</body>
</html>