<?php
/**
 * Intercity Zambia Theme Functions
 *
 * @package IntercityZambia
 * @since 1.0.0
 * <AUTHOR> Computaz
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('INTERCITY_THEME_VERSION', '1.0.0');
define('INTERCITY_THEME_DIR', get_template_directory());
define('INTERCITY_THEME_URI', get_template_directory_uri());

/**
 * Theme Setup
 */
function intercity_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));
    add_theme_support('customize-selective-refresh-widgets');
    add_theme_support('responsive-embeds');
    add_theme_support('align-wide');
    add_theme_support('wp-block-styles');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'intercityzambia'),
        'footer' => __('Footer Menu', 'intercityzambia'),
        'mobile' => __('Mobile Menu', 'intercityzambia'),
    ));
    
    // Debug: Log registered menus
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('Intercity Theme: Navigation menus registered');
    }
    
    // Add image sizes
    add_image_size('bus-thumbnail', 300, 200, true);
    add_image_size('route-card', 400, 250, true);
    add_image_size('operator-logo', 150, 100, true);
    add_image_size('hero-banner', 1280, 720, true);
    add_image_size('page-image', 1280, 500, true);
    add_image_size('cards', 640, 360, true);
    add_image_size('gallery', 1280, 720, true);
    
    // Load text domain
    load_theme_textdomain('intercityzambia', INTERCITY_THEME_DIR . '/languages');
}
add_action('after_setup_theme', 'intercity_setup');

/**
 * Enqueue Scripts and Styles
 */
function intercity_scripts() {
    // Main stylesheet
    wp_enqueue_style(
        'intercity-main',
        INTERCITY_THEME_URI . '/assets/css/main.min.css',
        array(),
        INTERCITY_THEME_VERSION
    );

    // Add dropdown menu CSS
    wp_add_inline_style('intercity-main', '
        /* Dropdown Menu Styles */
        .nav-menu {
            position: relative;
        }

        .nav-menu .menu-item {
            position: relative;
        }

        .nav-menu .sub-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
            padding: 0.5rem 0;
            margin: 0;
            list-style: none;
        }

        .nav-menu .menu-item:hover > .sub-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        /* Dropdown icon rotation on hover */
        .nav-menu .has-dropdown:hover .dropdown-icon {
            transform: rotate(180deg);
        }

        .nav-menu .sub-menu .menu-item {
            display: block;
            width: 100%;
        }

        .nav-menu .sub-menu .nav-link {
            display: block;
            padding: 0.5rem 1rem;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            border-radius: 0;
        }

        .nav-menu .sub-menu .nav-link:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        /* Mobile menu styles */
        .mobile-nav .sub-menu {
            position: static;
            opacity: 1;
            visibility: visible;
            transform: none;
            box-shadow: none;
            border: none;
            background: transparent;
            padding-left: 1rem;
            margin-top: 0.5rem;
        }

        .mobile-nav .mobile-sub-menu {
            display: none;
            background: #f8fafc;
            border-radius: 0.5rem;
            margin-top: 0.5rem;
            padding: 0.5rem 0;
        }

        .mobile-nav .has-dropdown-mobile.active .mobile-sub-menu {
            display: block;
        }

        .mobile-nav .has-dropdown-mobile.active .mobile-dropdown-icon {
            transform: rotate(90deg);
        }

        /* Mobile dropdown toggle functionality */
        .mobile-nav .has-dropdown-mobile > a {
            cursor: pointer;
        }

        .mobile-nav .mobile-dropdown-icon {
            transition: transform 0.2s ease-in-out;
        }

        /* Enhanced dropdown icon styles */
        .dropdown-icon {
            flex-shrink: 0;
        }

        .mobile-dropdown-icon {
            flex-shrink: 0;
        }

        /* Better spacing for dropdown links */
        .nav-menu .has-dropdown > .nav-link {
            padding-right: 0.75rem;
        }

        /* Page Image Styles */
        .page-image-container {
            width: 100%;
            margin-bottom: 2rem;
            overflow: hidden;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .page-image {
            width: 100%;
            height: auto;
            display: block;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .page-image:hover {
            transform: scale(1.02);
        }

        /* Page layout adjustments */
        .page-content {
            transition: margin-top 0.3s ease;
        }

        .page-content.has-page-image {
            margin-top: 0;
        }

        .page-content.no-page-image {
            margin-top: 2rem;
        }

        /* Responsive page image */
        @media (max-width: 768px) {
            .page-image-container {
                margin-bottom: 1.5rem;
                border-radius: 0.25rem;
            }
        }
    ');
    
    // Main JavaScript bundle
    wp_enqueue_script(
        'intercity-main',
        INTERCITY_THEME_URI . '/assets/js/main.min.js',
        array(),
        INTERCITY_THEME_VERSION,
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('intercity-main', 'intercity_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('intercity_nonce'),
        'site_url' => home_url(),
        'theme_url' => INTERCITY_THEME_URI,
    ));
    
    // Add mobile dropdown toggle script
    wp_add_inline_script('intercity-main', "
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile dropdown toggle functionality
            const mobileDropdowns = document.querySelectorAll('.mobile-nav .has-dropdown-mobile > a');
            
            mobileDropdowns.forEach(function(dropdown) {
                dropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const parentLi = this.closest('.has-dropdown-mobile');
                    const isActive = parentLi.classList.contains('active');
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.mobile-nav .has-dropdown-mobile').forEach(function(item) {
                        if (item !== parentLi) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    if (isActive) {
                        parentLi.classList.remove('active');
                    } else {
                        parentLi.classList.add('active');
                    }
                });
            });
        });
    ");
    
    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'intercity_scripts');

/**
 * Admin Scripts for Route Management
 */
function intercity_admin_scripts($hook) {
    if (($hook == 'post.php' || $hook == 'post-new.php') && 
        (isset($_GET['post_type']) && $_GET['post_type'] == 'route' || 
         (isset($_GET['post']) && is_numeric($_GET['post']) && get_post_type(intval($_GET['post'])) == 'route'))) {
        
        wp_enqueue_script(
            'intercity-admin-route',
            INTERCITY_THEME_URI . '/assets/js/admin-route.js',
            array('jquery'),
            INTERCITY_THEME_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('intercity-admin-route', 'intercity_admin', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('intercity_admin_nonce')
        ));
    }
    
    // Service management scripts
    if (($hook == 'post.php' || $hook == 'post-new.php') && 
        (isset($_GET['post_type']) && $_GET['post_type'] == 'service' || 
         (isset($_GET['post']) && is_numeric($_GET['post']) && get_post_type(intval($_GET['post'])) == 'service'))) {
        
        wp_enqueue_script(
            'intercity-admin-service',
            INTERCITY_THEME_URI . '/assets/js/admin-service.js',
            array('jquery'),
            INTERCITY_THEME_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('intercity-admin-service', 'intercity_admin', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('intercity_admin_nonce')
        ));
    }
}
add_action('admin_enqueue_scripts', 'intercity_admin_scripts');

/**
 * Include Custom Files
 */
require_once INTERCITY_THEME_DIR . '/inc/customizer.php';
require_once INTERCITY_THEME_DIR . '/inc/template-functions.php';
require_once INTERCITY_THEME_DIR . '/inc/custom-post-types.php';
require_once INTERCITY_THEME_DIR . '/inc/custom-fields.php';

require_once INTERCITY_THEME_DIR . '/inc/data-import.php';



/**
 * Widget Areas
 */
function intercity_widgets_init() {
    register_sidebar(array(
        'name' => __('Sidebar', 'intercityzambia'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here.', 'intercityzambia'),
        'before_widget' => '<div class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
}
add_action('widgets_init', 'intercity_widgets_init');

/**
 * Custom excerpt length
 */
function intercity_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'intercity_excerpt_length', 999);

/**
 * Custom Excerpt More
 */
function intercity_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'intercity_excerpt_more');



/**
 * PHP 8+ Compatibility - Basic theme-level compatibility
 * TEMPORARILY DISABLED TO FIX _acf_changed DATABASE ERROR
 */
function intercity_handle_php8_compatibility() {
    // TEMPORARILY DISABLE ACF FILTER TO FIX DATABASE ERROR
    // if (function_exists('acf_get_field')) {
    //     add_filter('acf/load_value', 'intercity_ensure_acf_string_value', 10, 3);
    //
    //     // Temporarily disable our filter during ACF save operations to prevent interference
    //     add_action('acf/save_post', 'intercity_disable_php8_filter_during_save', 1);
    //     add_action('acf/save_post', 'intercity_re_enable_php8_filter_after_save', 999);
    // }

    // Add SCF compatibility
    add_action('init', 'intercity_setup_scf_compatibility', 20);
}
add_action('init', 'intercity_handle_php8_compatibility', 1);

/**
 * Temporarily disable PHP 8+ filter during ACF save to prevent interference with _acf_changed
 */
function intercity_disable_php8_filter_during_save($post_id) {
    remove_filter('acf/load_value', 'intercity_ensure_acf_string_value', 10);
}

/**
 * Re-enable PHP 8+ filter after ACF save is complete
 */
function intercity_re_enable_php8_filter_after_save($post_id) {
    add_filter('acf/load_value', 'intercity_ensure_acf_string_value', 10, 3);
}

/**
 * Debug ACF meta updates (temporary - remove after testing)
 * Add ?debug_acf=1 to page edit URL to see debug info
 */
function intercity_debug_acf_meta_updates() {
    if (!current_user_can('edit_posts') || !isset($_GET['debug_acf'])) {
        return;
    }

    add_action('admin_notices', function() {
        echo '<div class="notice notice-info"><p><strong>ACF Debug Mode Active:</strong> Monitoring meta updates for _acf_changed</p></div>';
    });

    // Log when _acf_changed is being updated
    add_action('update_post_meta', function($meta_id, $post_id, $meta_key, $meta_value) {
        if ($meta_key === '_acf_changed') {
            error_log("ACF Debug: _acf_changed updated for post {$post_id} with value: " . print_r($meta_value, true));
        }
    }, 10, 4);

    // Log failed meta updates
    add_action('update_post_meta', function($meta_id, $post_id, $meta_key, $meta_value) {
        if ($meta_key === '_acf_changed' && $meta_id === false) {
            error_log("ACF Debug: FAILED to update _acf_changed for post {$post_id}");
        }
    }, 10, 4);
}
add_action('admin_init', 'intercity_debug_acf_meta_updates');

/**
 * Comprehensive ACF debugging function
 * Add ?debug_acf_comprehensive=1 to see detailed debug info
 */
function intercity_comprehensive_acf_debug() {
    if (!current_user_can('edit_posts') || !isset($_GET['debug_acf_comprehensive'])) {
        return;
    }

    add_action('admin_notices', function() {
        global $post, $wpdb;
        if (!$post) return;

        echo '<div class="notice notice-info" style="padding: 20px; font-family: monospace; max-height: 400px; overflow-y: auto;">';
        echo '<h3>ACF Comprehensive Debug Info</h3>';
        echo '<p><strong>Post ID:</strong> ' . $post->ID . '</p>';
        echo '<p><strong>Post Type:</strong> ' . $post->post_type . '</p>';
        echo '<p><strong>Current User Can Edit:</strong> ' . (current_user_can('edit_post', $post->ID) ? 'YES' : 'NO') . '</p>';
        echo '<p><strong>ACF Active:</strong> ' . (function_exists('acf_get_field') ? 'YES' : 'NO') . '</p>';

        // Check if _acf_changed exists
        $acf_changed = get_post_meta($post->ID, '_acf_changed', true);
        echo '<p><strong>_acf_changed value:</strong> ' . ($acf_changed ? print_r($acf_changed, true) : 'EMPTY/NULL') . '</p>';

        // Test meta update capability
        $test_result = update_post_meta($post->ID, '_test_meta_update', time());
        echo '<p><strong>Test Meta Update Result:</strong> ' . ($test_result ? 'SUCCESS' : 'FAILED') . '</p>';

        // Check database connection and table structure
        echo '<p><strong>Database Connection:</strong> ' . ($wpdb->last_error ? 'ERROR: ' . $wpdb->last_error : 'OK') . '</p>';

        // Check postmeta table structure
        $table_info = $wpdb->get_results("DESCRIBE {$wpdb->postmeta}");
        echo '<p><strong>Postmeta Table Structure:</strong><br>';
        foreach ($table_info as $column) {
            echo '&nbsp;&nbsp;' . $column->Field . ' (' . $column->Type . ')<br>';
        }
        echo '</p>';

        // Check for any active meta update filters
        global $wp_filter;
        if (isset($wp_filter['update_post_meta'])) {
            echo '<p><strong>Active update_post_meta filters:</strong><br>';
            foreach ($wp_filter['update_post_meta']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    $function_name = 'Unknown';
                    try {
                        if (is_array($callback['function'])) {
                            if (is_object($callback['function'][0])) {
                                $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                            } else {
                                $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                            }
                        } elseif (is_string($callback['function'])) {
                            $function_name = $callback['function'];
                        } elseif ($callback['function'] instanceof Closure) {
                            $function_name = 'Closure';
                        }
                    } catch (Exception $e) {
                        $function_name = 'Error getting function name';
                    }
                    echo '&nbsp;&nbsp;Priority ' . $priority . ': ' . $function_name . '<br>';
                }
            }
            echo '</p>';
        }

        // Check for any active ACF filters
        $acf_filters = array('acf/save_post', 'acf/load_value', 'acf/update_value', 'acf/validate_value');
        foreach ($acf_filters as $filter_name) {
            if (isset($wp_filter[$filter_name])) {
                echo '<p><strong>Active ' . $filter_name . ' filters:</strong><br>';
                foreach ($wp_filter[$filter_name]->callbacks as $priority => $callbacks) {
                    foreach ($callbacks as $callback) {
                        $function_name = 'Unknown';
                        try {
                            if (is_array($callback['function'])) {
                                if (is_object($callback['function'][0])) {
                                    $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                                } else {
                                    $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                                }
                            } elseif (is_string($callback['function'])) {
                                $function_name = $callback['function'];
                            } elseif ($callback['function'] instanceof Closure) {
                                $function_name = 'Closure';
                            }
                        } catch (Exception $e) {
                            $function_name = 'Error getting function name';
                        }
                        echo '&nbsp;&nbsp;Priority ' . $priority . ': ' . $function_name . '<br>';
                    }
                }
                echo '</p>';
            }
        }

        echo '</div>';
    });
}
add_action('admin_init', 'intercity_comprehensive_acf_debug');

/**
 * Hook into meta update process to debug _acf_changed failures
 */
function intercity_debug_meta_update_process() {
    // Hook before meta update
    add_action('update_post_meta', function($meta_id, $post_id, $meta_key, $meta_value) {
        if ($meta_key === '_acf_changed') {
            error_log("ACF Debug: Attempting to update _acf_changed for post {$post_id}");
            error_log("ACF Debug: Meta value: " . print_r($meta_value, true));

            // Check if the update actually succeeded
            $updated_value = get_post_meta($post_id, '_acf_changed', true);
            error_log("ACF Debug: Current _acf_changed value after update: " . print_r($updated_value, true));
        }
    }, 10, 4);

    // Hook into WordPress errors
    add_action('wp_error_added', function($code, $message, $data, $wp_error) {
        if (strpos($message, '_acf_changed') !== false || strpos($message, 'meta value') !== false) {
            error_log("ACF Debug: WordPress error related to meta: {$code} - {$message}");
        }
    }, 10, 4);
}
add_action('init', 'intercity_debug_meta_update_process');

/**
 * Workaround for _acf_changed database update issue
 * Manually update _acf_changed when ACF fields are saved
 */
function intercity_fix_acf_changed_update() {
    // Hook into WordPress save_post with high priority (after ACF)
    add_action('save_post', function($post_id) {
        // Only run for pages (where the issue occurs)
        if (get_post_type($post_id) !== 'page') {
            return;
        }

        // Check if this is an autosave or revision
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }

        // Check if ACF fields were submitted
        if (isset($_POST['acf']) && is_array($_POST['acf'])) {
            // Manually update _acf_changed to current timestamp
            $result = update_post_meta($post_id, '_acf_changed', time());

            // Log the result for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ACF Workaround: Manually updated _acf_changed for post {$post_id}. Result: " . ($result ? 'SUCCESS' : 'FAILED'));
            }
        }
    }, 99); // High priority to run after ACF
}
add_action('init', 'intercity_fix_acf_changed_update');

/**
 * SCF (Smart Custom Fields) Compatibility
 * SCF is an ACF fork that uses different function names
 */
function intercity_setup_scf_compatibility() {
    // Check if SCF is active (it uses different function names than ACF)
    if (function_exists('SCF::get')) {
        // Create get_field() compatibility function for SCF
        if (!function_exists('get_field')) {
            function get_field($field_name, $post_id = null) {
                if (!$post_id) {
                    $post_id = get_the_ID();
                }
                return SCF::get($field_name, $post_id);
            }
        }

        // Create update_field() compatibility function for SCF
        if (!function_exists('update_field')) {
            function update_field($field_name, $value, $post_id = null) {
                if (!$post_id) {
                    $post_id = get_the_ID();
                }
                return SCF::save($field_name, $value, $post_id);
            }
        }

        // Add debug info for SCF
        add_action('wp_head', function() {
            if (current_user_can('edit_posts')) {
                echo '<!-- SCF Active: YES -->';
                echo '<!-- SCF Version: ' . (defined('SMART_CUSTOM_FIELDS_VERSION') ? SMART_CUSTOM_FIELDS_VERSION : 'Unknown') . ' -->';

                // Test SCF on about-us page
                if (is_page('about-us')) {
                    $test_image = SCF::get('page_image', get_the_ID());
                    echo '<!-- SCF Test on About Us: ' . (empty($test_image) ? 'EMPTY' : (is_array($test_image) ? 'ARRAY' : 'VALUE')) . ' -->';
                    if (!empty($test_image)) {
                        if (is_array($test_image)) {
                            echo '<!-- SCF Image Keys: ' . implode(', ', array_keys($test_image)) . ' -->';
                        } else {
                            echo '<!-- SCF Image Value: ' . $test_image . ' -->';
                        }
                    }
                }
            }
        });
    } else {
        // Add debug info when SCF is not active
        add_action('wp_head', function() {
            if (current_user_can('edit_posts')) {
                echo '<!-- SCF Active: NO -->';
                echo '<!-- ACF Active: ' . (function_exists('get_field') ? 'YES' : 'NO') . ' -->';
            }
        });
    }
}

/**
 * Ensure ACF field values are safe for PHP 8+
 */
function intercity_ensure_acf_string_value($value, $post_id, $field) {
    // Skip ACF internal meta keys to prevent interference with ACF's internal processes
    if (is_array($field) && isset($field['name'])) {
        $acf_internal_keys = array(
            '_acf_changed',
            'acf_changed',
            '_acf_version',
            'acf_version'
        );

        if (in_array($field['name'], $acf_internal_keys)) {
            return $value;
        }
    }

    // Handle null values that could cause deprecation warnings
    if (is_null($value)) {
        // Return appropriate default based on field type
        if (isset($field['type'])) {
            switch ($field['type']) {
                case 'text':
                case 'textarea':
                case 'email':
                case 'url':
                case 'password':
                    return '';
                case 'number':
                    return 0;
                case 'true_false':
                    return false;
                case 'select':
                case 'checkbox':
                case 'radio':
                    return array();
                default:
                    return '';
            }
        }
        return '';
    }
    return $value;
}

/**
 * WordPress Optimization
 */
function intercity_optimize_wordpress() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');

    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');
}
add_action('init', 'intercity_optimize_wordpress');

/**
 * Fix Admin Bar Array Key Error
 * This prevents the "Undefined array key 1" error in wp-admin-bar.php
 */
function intercity_fix_admin_bar_error() {
    if (!is_admin() && is_user_logged_in()) {
        // Ensure admin bar data is properly initialized
        add_action('wp_before_admin_bar_render', 'intercity_clean_admin_bar_data');
    }
}
add_action('init', 'intercity_fix_admin_bar_error');

/**
 * Clean admin bar data before rendering
 */
function intercity_clean_admin_bar_data() {
    global $wp_admin_bar;
    
    if (!$wp_admin_bar) {
        return;
    }
    
    // Get all admin bar nodes
    $all_toolbar_nodes = $wp_admin_bar->get_nodes();
    
    // Check and fix any malformed nodes
    foreach ($all_toolbar_nodes as $node) {
        if (is_object($node) && isset($node->href) && !is_string($node->href)) {
            $node->href = '';
        }
    }
}

/**
 * Alternative admin bar error suppression (if above doesn't work)
 * Remove this if the above solution works
 */
function intercity_suppress_admin_bar_errors() {
    if (!is_admin() && is_user_logged_in()) {
        add_action('wp_before_admin_bar_render', function() {
            // Temporarily suppress PHP warnings for admin bar
            $old_error_reporting = error_reporting();
            error_reporting($old_error_reporting & ~E_WARNING);
            
            // Restore error reporting after admin bar renders
            add_action('wp_after_admin_bar_render', function() use ($old_error_reporting) {
                error_reporting($old_error_reporting);
            });
        });
    }
}
// Uncomment the line below if the first solution doesn't work
// add_action('init', 'intercity_suppress_admin_bar_errors');

/**
 * Custom Walker for Desktop Navigation
 */
class Intercity_Walker_Nav_Menu extends Walker_Nav_Menu {

    // Start Level - outputs <ul>
    function start_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "\n$indent<ul class=\"sub-menu\">\n";
    }

    // End Level - outputs </ul>
    function end_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "$indent</ul>\n";
    }

    // Start Element - outputs <li><a>
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // Check if this menu item has children
        $has_children = in_array('menu-item-has-children', $classes);

        // Add nav-link class for styling
        if ($depth == 0) {
            $classes[] = 'nav-link';
            if ($has_children) {
                $classes[] = 'has-dropdown';
            }
        } else {
            $classes[] = 'nav-link';
        }

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        $link_class = ($depth == 0) ? 'nav-link' : 'nav-link';
        if ($has_children && $depth == 0) {
            $link_class .= ' flex items-center';
        }

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a class="' . $link_class . '"' . $attributes . '>';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        
        // Add dropdown icon for parent items
        if ($has_children && $depth == 0) {
            $item_output .= '<svg class="w-4 h-4 ml-1 transition-transform duration-200 dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>';
        }
        
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    // End Element - outputs </li>
    function end_el(&$output, $item, $depth = 0, $args = null) {
        $output .= "</li>\n";
    }
}

/**
 * Custom Walker for Mobile Navigation
 */
class Intercity_Walker_Mobile_Menu extends Walker_Nav_Menu {
    
    // Start Level - outputs <ul>
    function start_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "\n$indent<ul class=\"sub-menu mobile-sub-menu pl-4\">\n";
    }

    // End Level - outputs </ul>
    function end_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "$indent</ul>\n";
    }

    // Start Element - outputs <li><a>
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;
        
        // Check if this menu item has children
        $has_children = in_array('menu-item-has-children', $classes);

        // Add mobile-specific classes
        if ($depth == 0) {
            $classes[] = 'block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors';
            if ($has_children) {
                $classes[] = 'has-dropdown-mobile';
            }
        } else {
            $classes[] = 'block px-4 py-2 text-neutral-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors text-sm';
        }

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        $link_class = 'flex items-center justify-between w-full';
        if ($depth == 0) {
            $link_class .= ' mobile-nav-link';
        } else {
            $link_class .= ' mobile-nav-sublink';
        }

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a class="' . $link_class . '"' . $attributes . '>';
        $item_output .= '<span>' . apply_filters('the_title', $item->title, $item->ID) . '</span>';
        
        // Add dropdown icon for parent items
        if ($has_children && $depth == 0) {
            $item_output .= '<svg class="w-4 h-4 transition-transform duration-200 mobile-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>';
        }
        
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    // End Element - outputs </li>
    function end_el(&$output, $item, $depth = 0, $args = null) {
        $output .= "</li>\n";
    }
}

/**
 * Debug function to show menu status
 */
function intercity_debug_menu() {
    if (current_user_can('manage_options')) {
        echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">';
        echo '<strong>Menu Debug Info:</strong><br>';
        echo 'Has Primary Menu: ' . (has_nav_menu('primary') ? 'YES' : 'NO') . '<br>';
        echo 'Registered Locations: ' . implode(', ', array_keys(get_registered_nav_menus())) . '<br>';
        $nav_menu_locations = get_nav_menu_locations();
        echo 'Assigned Locations: ' . print_r($nav_menu_locations, true) . '<br>';

        // Check menu items
        $locations = get_nav_menu_locations();
        if (isset($locations['primary'])) {
            $menu = wp_get_nav_menu_object($locations['primary']);
            if ($menu) {
                $menu_items = wp_get_nav_menu_items($menu->term_id);
                echo 'Menu Items Count: ' . count($menu_items) . '<br>';
                if ($menu_items) {
                    echo 'First Item Title: "' . $menu_items[0]->title . '"<br>';
                    echo 'First Item URL: "' . $menu_items[0]->url . '"<br>';
                }
            }
        }
        echo '</div>';
    }
}

/**
 * Function to fix broken menu items
 */
function intercity_fix_menu_items() {
    if (!current_user_can('manage_options')) {
        return false;
    }

    $locations = get_nav_menu_locations();
    if (isset($locations['primary'])) {
        $menu = wp_get_nav_menu_object($locations['primary']);
        if ($menu) {
            $menu_items = wp_get_nav_menu_items($menu->term_id);
            if ($menu_items) {
                $fixed_count = 0;
                foreach ($menu_items as $item) {
                    // Check if item has empty title or URL
                    if (empty($item->title) || empty($item->url)) {
                        // Delete broken menu item
                        wp_delete_post($item->ID, true);
                        $fixed_count++;
                    }
                }

                if ($fixed_count > 0) {
                    // Add some default menu items
                    wp_update_nav_menu_item($menu->term_id, 0, array(
                        'menu-item-title' => 'Home',
                        'menu-item-url' => home_url('/'),
                        'menu-item-status' => 'publish'
                    ));

                    wp_update_nav_menu_item($menu->term_id, 0, array(
                        'menu-item-title' => 'Find Services',
                        'menu-item-url' => home_url('/find-services'),
                        'menu-item-status' => 'publish'
                    ));

                    wp_update_nav_menu_item($menu->term_id, 0, array(
                        'menu-item-title' => 'Routes',
                        'menu-item-url' => home_url('/routes'),
                        'menu-item-status' => 'publish'
                    ));

                    wp_update_nav_menu_item($menu->term_id, 0, array(
                        'menu-item-title' => 'About',
                        'menu-item-url' => home_url('/about'),
                        'menu-item-status' => 'publish'
                    ));

                    wp_update_nav_menu_item($menu->term_id, 0, array(
                        'menu-item-title' => 'Contact',
                        'menu-item-url' => home_url('/contact'),
                        'menu-item-status' => 'publish'
                    ));

                    return $fixed_count;
                }
            }
        }
    }
    return false;
}

/**
 * Create a default menu if none exists or if menu items are empty
 */
function intercity_create_default_menu() {
    // Only run for admins and if no proper menu exists
    if (!current_user_can('manage_options')) {
        return;
    }

    $locations = get_nav_menu_locations();
    $menu_exists = false;

    if (isset($locations['primary'])) {
        $menu = wp_get_nav_menu_object($locations['primary']);
        if ($menu) {
            $menu_items = wp_get_nav_menu_items($menu->term_id);
            // Check if menu has items with proper titles
            if ($menu_items && !empty($menu_items[0]->title)) {
                $menu_exists = true;
            }
        }
    }

    if (!$menu_exists) {
        // Create a new menu
        $menu_id = wp_create_nav_menu('Primary Navigation');

        if (!is_wp_error($menu_id)) {
            // Add menu items
            wp_update_nav_menu_item($menu_id, 0, array(
                'menu-item-title' => 'Home',
                'menu-item-url' => home_url('/'),
                'menu-item-status' => 'publish'
            ));

            wp_update_nav_menu_item($menu_id, 0, array(
                'menu-item-title' => 'Find Services',
                'menu-item-url' => home_url('/find-services'),
                'menu-item-status' => 'publish'
            ));

            wp_update_nav_menu_item($menu_id, 0, array(
                'menu-item-title' => 'Routes',
                'menu-item-url' => home_url('/routes'),
                'menu-item-status' => 'publish'
            ));

            wp_update_nav_menu_item($menu_id, 0, array(
                'menu-item-title' => 'About',
                'menu-item-url' => home_url('/about'),
                'menu-item-status' => 'publish'
            ));

            wp_update_nav_menu_item($menu_id, 0, array(
                'menu-item-title' => 'Contact',
                'menu-item-url' => home_url('/contact'),
                'menu-item-status' => 'publish'
            ));

            // Assign menu to location
            $locations = get_theme_mod('nav_menu_locations');
            $locations['primary'] = $menu_id;
            set_theme_mod('nav_menu_locations', $locations);
        }
    }
}
// Run this once to create default menu
add_action('admin_init', 'intercity_create_default_menu');

/**
 * Add admin menu for fixing menus
 */
function intercity_add_menu_fix_page() {
    add_submenu_page(
        'themes.php',
        'Fix Menu Items',
        'Fix Menu Items',
        'manage_options',
        'intercity-fix-menu',
        'intercity_menu_fix_page'
    );
}
add_action('admin_menu', 'intercity_add_menu_fix_page');

/**
 * Menu fix admin page
 */
function intercity_menu_fix_page() {
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    $message = '';

    if (isset($_POST['fix_menu']) && wp_verify_nonce($_POST['_wpnonce'], 'fix_menu_action')) {
        $fixed_count = intercity_fix_menu_items();
        if ($fixed_count !== false && $fixed_count > 0) {
            $message = "<div class='notice notice-success'><p>Fixed $fixed_count broken menu items and added default items.</p></div>";
        } elseif ($fixed_count === 0) {
            $message = "<div class='notice notice-info'><p>No broken menu items found.</p></div>";
        } else {
            $message = "<div class='notice notice-error'><p>No menu found to fix.</p></div>";
        }
    }

    ?>
    <div class="wrap">
        <h1>Fix Menu Items</h1>
        <?php echo $message; ?>

        <div class="card">
            <h2>Menu Diagnostic</h2>
            <?php intercity_debug_menu(); ?>
        </div>

        <div class="card">
            <h2>Fix Broken Menu Items</h2>
            <p>This will remove any menu items with empty titles or URLs and replace them with default menu items.</p>

            <form method="post">
                <?php wp_nonce_field('fix_menu_action'); ?>
                <input type="submit" name="fix_menu" class="button button-primary" value="Fix Menu Items" />
            </form>
        </div>
    </div>
    <?php
}

/**
 * Fallback function for primary menu
 */
function intercity_default_menu() {
    echo '<div class="flex items-center space-x-8">';
    echo '<a href="' . home_url() . '" class="nav-link">' . __('Home', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/find-services') . '" class="nav-link">' . __('Find Services', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/routes') . '" class="nav-link">' . __('Routes', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/destinations') . '" class="nav-link">' . __('Destinations', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/operators') . '" class="nav-link">' . __('Operators', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/contact') . '" class="nav-link">' . __('Contact', 'intercityzambia') . '</a>';
    echo '</div>';
}

/**
 * Fallback function for mobile menu
 */
function intercity_mobile_menu() {
    echo '<div class="space-y-2">';
    echo '<a href="' . home_url() . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Home', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/find-services') . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Find Services', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/routes') . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Routes', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/destinations') . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Destinations', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/operators') . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Operators', 'intercityzambia') . '</a>';
    echo '<a href="' . home_url('/contact') . '" class="block px-4 py-3 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">' . __('Contact', 'intercityzambia') . '</a>';
    echo '</div>';
}

/**
 * Include additional theme functionality
 */
// Include custom post types
require_once get_template_directory() . '/inc/custom-post-types.php';

// Include custom fields
require_once get_template_directory() . '/inc/custom-fields.php';

/**
 * Customize how service names appear in SCF post object fields
 */
function intercity_format_service_name_titles($title, $post, $field, $post_id) {
    // Ensure we always return a string
    $safe_title = is_string($title) ? $title : '';
    
    // Check if ACF is active and we have valid parameters
    if (!function_exists('get_field') || !$post || !is_object($post) || !isset($post->post_type) || !isset($post->ID)) {
        return $safe_title;
    }
    
    // Check if field is valid
    if (!$field || !is_array($field) || !isset($field['type'])) {
        return $safe_title;
    }
    
    // Only apply to service_name post type when used in post_object fields
    if ($post->post_type == 'service_name' && $field['type'] == 'post_object') {
        // Get the custom field value
        $service_name_value = get_field('service_name_value', $post->ID);
        if ($service_name_value && is_string($service_name_value)) {
            return $service_name_value;
        }
    }
    return $safe_title;
}
/**
 * Intercept the value when it's loaded from database (this is the key fix)
 */
function intercity_load_service_name_value($value, $post_id, $field) {
    // Check if ACF is active and we have valid parameters
    if (!function_exists('get_field') || !$field || !is_array($field) || !isset($field['name']) || !isset($field['type'])) {
        return $value;
    }
    
    // Ensure we have a valid post_id
    if (!$post_id || (!is_numeric($post_id) && (!is_object($post_id) || !isset($post_id->ID)))) {
        return $value;
    }
    
    // Only apply to service_name field
    if ($field['name'] == 'service_name' && $field['type'] == 'post_object') {
        // Handle both numeric ID and post object
        $service_name_post = null;
        
        if (is_numeric($value) && $value > 0) {
            $service_name_post = get_post($value);
        } elseif (is_object($value) && isset($value->ID)) {
            $service_name_post = $value;
        }
        
        if ($service_name_post && is_object($service_name_post) && isset($service_name_post->post_type) && $service_name_post->post_type == 'service_name' && isset($service_name_post->ID)) {
            $service_name_value = get_field('service_name_value', $service_name_post->ID);
            if ($service_name_value && is_string($service_name_value)) {
                // Create a modified post object with custom title
                $modified_value = clone $service_name_post;
                $modified_value->post_title = $service_name_value;
                return $modified_value;
            } elseif (isset($service_name_post->post_title) && is_string($service_name_post->post_title)) {
                // If no custom value, ensure we have a valid post_title
                $modified_value = clone $service_name_post;
                $modified_value->post_title = $service_name_post->post_title;
                return $modified_value;
            } else {
                // Fallback to ensure we have a valid post_title
                $modified_value = clone $service_name_post;
                $modified_value->post_title = '';
                return $modified_value;
            }
        }
    }
    
    // Ensure we never return null for post object fields
    if (is_null($value) && isset($field['type']) && $field['type'] == 'post_object') {
        return false;
    }
    
    return $value;
}

/**
 * Also format the display value for service names in admin
 */
function intercity_format_service_name_display($value, $post_id, $field) {
    // Check if ACF is active and we have valid parameters
    if (!function_exists('get_field') || !$field || !is_array($field) || !isset($field['name']) || !isset($field['type'])) {
        return $value;
    }
    
    // Ensure we have a valid post_id
    if (!$post_id || (!is_numeric($post_id) && (!is_object($post_id) || !isset($post_id->ID)))) {
        return $value;
    }
    
    // Only apply to service_name field
    if ($field['name'] == 'service_name' && $field['type'] == 'post_object') {
        if (is_object($value) && isset($value->post_type) && $value->post_type == 'service_name' && isset($value->ID)) {
            $service_name_value = get_field('service_name_value', $value->ID);
            if ($service_name_value && is_string($service_name_value)) {
                // Create a copy of the object and modify the post_title
                $modified_value = clone $value;
                $modified_value->post_title = $service_name_value;
                return $modified_value;
            } elseif (isset($value->post_title) && is_string($value->post_title)) {
                // If no custom value, ensure we have a valid post_title
                $modified_value = clone $value;
                $modified_value->post_title = $value->post_title;
                return $modified_value;
            } else {
                // Fallback to ensure we have a valid post_title
                $modified_value = clone $value;
                $modified_value->post_title = '';
                return $modified_value;
            }
        }
    }
    
    // Ensure we never return null for post object fields
    if (is_null($value) && isset($field['type']) && $field['type'] == 'post_object') {
        return false;
    }
    
    return $value;
}

/**
 * Store the service name display value separately when saving a service
 */
function intercity_save_service_name_display($post_id) {
    // Check if ACF is active
    if (!function_exists('get_field')) {
        return;
    }
    
    // Only run for service post type
    if (!$post_id || !is_numeric($post_id) || get_post_type($post_id) !== 'service') {
        return;
    }
    
    // Get the service_name post object
    $service_name_post = get_field('service_name', $post_id);
    
    if ($service_name_post && is_object($service_name_post) && isset($service_name_post->ID)) {
        // Get the custom service_name_value
        $service_name_value = get_field('service_name_value', $service_name_post->ID);
        
        if ($service_name_value && is_string($service_name_value)) {
            // Store the display value in a separate meta field
            update_post_meta($post_id, '_service_name_display', sanitize_text_field($service_name_value));
        } elseif (!empty($service_name_post->post_title)) {
            // Fallback to post title if no custom value
            update_post_meta($post_id, '_service_name_display', sanitize_text_field($service_name_post->post_title));
        }
    }
}

// TEMPORARILY DISABLED ALL ACF HOOKS TO FIX DATABASE ERROR
// Only add ACF hooks if ACF is active
// if (function_exists('get_field')) {
//     // TEMPORARILY DISABLED TO TEST FOR DEPRECATION WARNINGS
//     // add_filter('acf/fields/post_object/result', 'intercity_format_service_name_titles', 10, 4);
//     // add_filter('acf/load_value/name=service_name', 'intercity_load_service_name_value', 10, 3);
//     // add_filter('acf/format_value/type=post_object', 'intercity_format_service_name_display', 10, 3);
//
//     // Always add the save hook
//     add_action('acf/save_post', 'intercity_save_service_name_display', 20);
// }



/**
 * Helper function to get the service name display value
 * 
 * Usage:
 * echo intercity_get_service_name_display(); // Uses current post
 * echo intercity_get_service_name_display(250); // Uses specific service ID
 */
function intercity_get_service_name_display($post_id = null) {
    // Check if ACF is active
    if (!function_exists('get_field')) {
        return '';
    }
    
    // If no post ID provided, use current post
    if (!$post_id || !is_numeric($post_id)) {
        global $post;
        if (!$post || !is_object($post) || !isset($post->ID) || get_post_type($post->ID) !== 'service') {
            return '';
        }
        $post_id = $post->ID;
    }
    
    // First try to get the stored display value
    $display_value = get_post_meta($post_id, '_service_name_display', true);
    
    if ($display_value && is_string($display_value)) {
        return $display_value;
    }
    
    // Fall back to getting it from the post object
    $service_name_post = get_field('service_name', $post_id);
    
    if ($service_name_post && is_object($service_name_post) && isset($service_name_post->ID)) {
        $service_name_value = get_field('service_name_value', $service_name_post->ID);
        if ($service_name_value && is_string($service_name_value)) {
            return $service_name_value;
        }
        if (!empty($service_name_post->post_title)) {
            return $service_name_post->post_title;
        }
    }
    
    return '';
}

/**
 * Helper function to get the bus operator for a service
 * 
 * Usage:
 * $operator = intercity_get_service_bus_operator(); // Uses current post
 * $operator = intercity_get_service_bus_operator(250); // Uses specific service ID
 * 
 * @param int|null $post_id Service post ID (optional)
 * @return WP_Post|null The bus operator post object or null if not found
 */
function intercity_get_service_bus_operator($post_id = null) {
    // Check if ACF is active
    if (!function_exists('get_field')) {
        return null;
    }
    
    // If no post ID provided, use current post
    if (!$post_id || !is_numeric($post_id)) {
        global $post;
        if (!$post || !is_object($post) || !isset($post->ID) || get_post_type($post->ID) !== 'service') {
            return null;
        }
        $post_id = $post->ID;
    }
    
    // Ensure we have a valid post ID
    if (!$post_id || !is_numeric($post_id)) {
        return null;
    }
    
    // First try to get the direct bus operator field
    $bus_operator = get_field('service_bus_operator', $post_id);
    
    if ($bus_operator && is_object($bus_operator) && isset($bus_operator->ID)) {
        return $bus_operator;
    }
    
    // Fall back to getting it through the bus relationship
    $bus = get_field('service_bus', $post_id);
    
    if ($bus && is_object($bus) && isset($bus->ID) && is_numeric($bus->ID)) {
        $bus_operator = get_field('operator', $bus->ID);
        if ($bus_operator && is_object($bus_operator) && isset($bus_operator->ID)) {
            return $bus_operator;
        }
    }
    
    return null;
}

/**
 * Helper function to get the bus operator name for a service
 * 
 * Usage:
 * echo intercity_get_service_bus_operator_name(); // Uses current post
 * echo intercity_get_service_bus_operator_name(250); // Uses specific service ID
 * 
 * @param int|null $post_id Service post ID (optional)
 * @return string The bus operator name or empty string if not found
 */
function intercity_get_service_bus_operator_name($post_id = null) {
    $bus_operator = intercity_get_service_bus_operator($post_id);
    
    if ($bus_operator && is_object($bus_operator) && !empty($bus_operator->post_title)) {
        return $bus_operator->post_title;
    }
    
    return '';
}

/**
 * Helper function to get services by bus operator
 * 
 * Usage:
 * $services = intercity_get_services_by_operator(123); // Get all services for operator ID 123
 * $services = intercity_get_services_by_operator(123, array('service_status' => 'scheduled')); // With additional filters
 * 
 * @param int $operator_id Bus operator post ID
 * @param array $additional_args Additional WP_Query arguments
 * @return WP_Post[] Array of service posts
 */
function intercity_get_services_by_operator($operator_id, $additional_args = array()) {
    if (!$operator_id) {
        return array();
    }
    
    $args = array_merge(array(
        'post_type' => 'service',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'service_bus_operator',
                'value' => $operator_id,
                'compare' => '='
            )
        ),
        'orderby' => 'meta_value',
        'meta_key' => 'service_date',
        'order' => 'ASC'
    ), $additional_args);
    
    $query = new WP_Query($args);
    return $query->posts;
}

// Debug function removed - no longer needed

// Debug notice removed - no longer needed

// JavaScript solution removed - was causing field dysfunction

/**
 * NEW APPROACH: Use acf/prepare_field to modify the field before display
 */
function intercity_scf_prepare_service_name_field($field) {
    if ($field['name'] == 'service_name' && $field['type'] == 'post_object') {
        if (isset($field['value']) && is_object($field['value']) && $field['value']->post_type == 'service_name') {
            $service_name_value = get_field('service_name_value', $field['value']->ID);
            if ($service_name_value) {
                // Clone the post object and modify the title
                $modified_value = clone $field['value'];
                $modified_value->post_title = $service_name_value;
                $field['value'] = $modified_value;
            }
        }
    }
    return $field;
}
// TEMPORARILY DISABLED TO FIX DATABASE ERROR
// add_filter('acf/prepare_field/name=service_name', 'intercity_scf_prepare_service_name_field', 10, 1);

// Duplicate function removed - using the one defined earlier

// Include template functions
require_once get_template_directory() . '/inc/template-functions.php';

// Include customizer
require_once get_template_directory() . '/inc/customizer.php';

/**
 * AJAX handler to filter buses by operator
 */
function intercity_filter_buses_by_operator() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'intercity_admin_nonce')) {
        wp_die('Security check failed');
    }
    
    $operator_id = isset($_POST['operator_id']) ? intval($_POST['operator_id']) : 0;
    
    if (!$operator_id) {
        wp_send_json_error('Invalid operator ID');
    }
    
    // Get buses for the selected operator
    $buses = get_posts(array(
        'post_type' => 'bus',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'operator',
                'value' => $operator_id,
                'compare' => '='
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    ));
    
    $bus_data = array();
    if ($buses && is_array($buses)) {
        foreach ($buses as $bus) {
            if (is_object($bus) && isset($bus->ID)) {
                $plate_number = get_field('plate_number', $bus->ID);
                $capacity = get_field('seating_capacity', $bus->ID);
                
                $bus_data[] = array(
                    'id' => $bus->ID,
                    'title' => !empty($bus->post_title) ? $bus->post_title : 'Untitled Bus',
                    'plate_number' => $plate_number ? $plate_number : '',
                    'capacity' => $capacity ? $capacity : 0
                );
            }
        }
    }
    
    wp_send_json_success($bus_data);
}
add_action('wp_ajax_filter_buses_by_operator', 'intercity_filter_buses_by_operator');

/**
 * AJAX handler to get bus operator for a specific bus
 */
function intercity_get_bus_operator() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'intercity_admin_nonce')) {
        wp_die('Security check failed');
    }
    
    $bus_id = isset($_POST['bus_id']) ? intval($_POST['bus_id']) : 0;
    
    if (!$bus_id) {
        wp_send_json_error('Invalid bus ID');
    }
    
    // Get the operator for this bus
    $operator = get_field('operator', $bus_id);
    
    if ($operator && is_object($operator) && isset($operator->ID)) {
        wp_send_json_success(array(
            'operator_id' => $operator->ID,
            'operator_name' => !empty($operator->post_title) ? $operator->post_title : 'Untitled Operator'
        ));
    } else {
        wp_send_json_error('No operator found for this bus');
    }
}
add_action('wp_ajax_get_bus_operator', 'intercity_get_bus_operator');

/**
 * Migration function to populate bus operator field for existing services
 * Run this once after adding the bus operator field to services
 */
function intercity_migrate_service_bus_operators() {
    // Check if ACF is active
    if (!function_exists('get_field') || !function_exists('update_field')) {
        return 0;
    }
    
    // Get all services
    $services = get_posts(array(
        'post_type' => 'service',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    if (!$services || !is_array($services)) {
        return 0;
    }
    
    $updated_count = 0;
    
    foreach ($services as $service) {
        if (!$service || !is_object($service) || !isset($service->ID) || !is_numeric($service->ID)) {
            continue;
        }
        
        // Check if bus operator field is already populated
        $existing_operator = get_field('service_bus_operator', $service->ID);
        
        if (!$existing_operator) {
            // Get the bus for this service
            $bus = get_field('service_bus', $service->ID);
            
            if ($bus && is_object($bus) && isset($bus->ID) && is_numeric($bus->ID)) {
                // Get the operator for this bus
                $operator = get_field('operator', $bus->ID);
                
                if ($operator && is_object($operator) && isset($operator->ID) && is_numeric($operator->ID)) {
                    // Update the service with the bus operator
                    update_field('service_bus_operator', intval($operator->ID), $service->ID);
                    $updated_count++;
                }
            }
        }
    }
    
    return $updated_count;
}

/**
 * Admin notice to run migration (only show to admins)
 */
function intercity_show_migration_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if migration was already run
    if (get_option('intercity_bus_operator_migration_run')) {
        return;
    }
    
    // Check if there are services without bus operators
    $services_without_operators = get_posts(array(
        'post_type' => 'service',
        'posts_per_page' => 1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'service_bus_operator',
                'compare' => 'NOT EXISTS'
            )
        )
    ));
    
    if (empty($services_without_operators) || !is_array($services_without_operators)) {
        // No services need migration
        update_option('intercity_bus_operator_migration_run', true);
        return;
    }
    
    $screen = get_current_screen();
    if ($screen && is_object($screen) && isset($screen->post_type) && $screen->post_type === 'service') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Bus Operator Migration:</strong> Some services need to be updated with bus operator information. ';
        echo '<a href="' . esc_url(admin_url('admin.php?page=intercity-migrate-operators')) . '">Click here to run the migration</a></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'intercity_show_migration_notice');

/**
 * Add admin menu item for migration
 */
function intercity_add_migration_menu() {
    add_submenu_page(
        null, // No parent menu (hidden)
        'Migrate Bus Operators',
        'Migrate Bus Operators',
        'manage_options',
        'intercity-migrate-operators',
        'intercity_migration_page'
    );
}
add_action('admin_menu', 'intercity_add_migration_menu');

/**
 * Migration page callback
 */
function intercity_migration_page() {
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }
    
    if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['_wpnonce'], 'intercity_migration')) {
        $updated_count = intercity_migrate_service_bus_operators();
        update_option('intercity_bus_operator_migration_run', true);
        
        echo '<div class="notice notice-success"><p>Migration completed! Updated ' . intval($updated_count) . ' services.</p></div>';
    }
    
    $services_count = wp_count_posts('service');
    $services_without_operators = get_posts(array(
        'post_type' => 'service',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'service_bus_operator',
                'compare' => 'NOT EXISTS'
            )
        )
    ));
    
    // Ensure we have valid data
    $total_services = ($services_count && is_object($services_count) && isset($services_count->publish)) 
        ? intval($services_count->publish) : 0;
    $services_needing_migration = ($services_without_operators && is_array($services_without_operators)) 
        ? count($services_without_operators) : 0;
    
    ?>
    <div class="wrap">
        <h1>Migrate Bus Operators</h1>
        <div class="card">
            <h2>Service Bus Operator Migration</h2>
            <p>This migration will populate the bus operator field for all existing services based on their assigned bus.</p>
            
            <ul>
                <li>Total services: <?php echo $total_services; ?></li>
                <li>Services without bus operator: <?php echo $services_needing_migration; ?></li>
            </ul>
            
            <?php if ($services_needing_migration > 0): ?>
                <form method="post">
                    <?php wp_nonce_field('intercity_migration'); ?>
                    <input type="submit" name="run_migration" class="button button-primary" value="Run Migration" />
                </form>
            <?php else: ?>
                <p><strong>All services already have bus operators assigned.</strong></p>
            <?php endif; ?>
        </div>
    </div>
    <?php
}