🔍 Menu Breakdown (with refined submenus)
1. About
submenus
About Us

Why Intercity Zambia

Our Partners



2. Stations
Main page:

Hero section with search/filter by town or province

Featured intercity stations (cards with image + link)

Compact list of all stations at the bottom

Considering:
💡 Consider adding a map view toggle here, powered by your station's “Map Location” data.

3. Routes
Route search form at the top

List of routes (most popular first)


4. Bus Operators
Grid/list view of all operators

Include: name, description, logo, contact, base station

Operator detail page: show their available services, routes, stations

5. Destinations
Submenu:
City Breaks

Curated travel ideas from major cities like Lusaka, Livingstone, Ndola etc.

All Destinations

Browse a full list of cities/towns, possibly grouped by province

Accommodation

Optional integration with booking options or a partner service

6. Log In
Styled as a button on the far right

Hover state and alternate text like “Manage Bookings” or “My Account” after login

Consider:
Search Icon in the nav for global search (stations, routes, operators)

Must have:
Mobile Menu: collapses into a hamburger with clear tap targets






<nav class="bg-white shadow-md font-sans" x-data="{ 
  mobileMenuOpen: false, 
  accountDropdownOpen: false,
  searchOpen: false,
  aboutDropdownOpen: false,
  destinationsDropdownOpen: false 
}">
  <div class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      
      <!-- Logo -->
      <div class="text-xl font-bold font-serif">
        <a href="/" class="text-gray-900 hover:text-purple-800 transition-colors">
          Intercity Zambia
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden lg:flex items-center space-x-8">
        
        <!-- Main Navigation -->
        <ul class="flex space-x-6 items-center text-sm font-medium text-gray-700">
          
          <!-- About Us -->
          <li class="relative">
            <button 
              class="flex items-center space-x-1 hover:text-purple-800 transition-colors py-2"
              @click="aboutDropdownOpen = !aboutDropdownOpen"
              @click.away="aboutDropdownOpen = false"
            >
              <span>About</span>
              <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': aboutDropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <ul 
              x-show="aboutDropdownOpen" 
              x-transition:enter="transition ease-out duration-200"
              x-transition:enter-start="opacity-0 scale-95"
              x-transition:enter-end="opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-150"
              x-transition:leave-start="opacity-100 scale-100"
              x-transition:leave-end="opacity-0 scale-95"
              class="absolute left-0 mt-2 w-56 bg-white border rounded-lg shadow-lg z-50"
              x-cloak
            >
              <li><a href="/about/about-us" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">About Us</a></li>
              <li><a href="/about/why-intercity-zambia" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">Why Intercity Zambia</a></li>
              <li><a href="/about/partners" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">Our Partners</a></li>
            </ul>
          </li>

          <!-- Stations -->
          <li><a href="/stations" class="hover:text-purple-800 transition-colors">Stations</a></li>

          <!-- Routes -->
          <li><a href="/routes" class="hover:text-purple-800 transition-colors">Routes</a></li>

          <!-- Bus Operators -->
          <li><a href="/bus-operators" class="hover:text-purple-800 transition-colors">Bus Operators</a></li>

          <!-- Destinations -->
          <li class="relative">
            <button 
              class="flex items-center space-x-1 hover:text-purple-800 transition-colors py-2"
              @click="destinationsDropdownOpen = !destinationsDropdownOpen"
              @click.away="destinationsDropdownOpen = false"
            >
              <span>Destinations</span>
              <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': destinationsDropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <ul 
              x-show="destinationsDropdownOpen" 
              x-transition:enter="transition ease-out duration-200"
              x-transition:enter-start="opacity-0 scale-95"
              x-transition:enter-end="opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-150"
              x-transition:leave-start="opacity-100 scale-100"
              x-transition:leave-end="opacity-0 scale-95"
              class="absolute left-0 mt-2 w-56 bg-white border rounded-lg shadow-lg z-50"
              x-cloak
            >
              <li><a href="/destinations/city-breaks" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">City Breaks</a></li>
              <li><a href="/destinations/all" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">All Destinations</a></li>
              <li><a href="/destinations/accommodation" class="block px-4 py-3 text-sm hover:bg-gray-50 hover:text-purple-800 transition-colors">Accommodation</a></li>
            </ul>
          </li>

        </ul>

        <!-- Search Icon -->
        <button 
          class="p-2 hover:bg-gray-100 rounded-full transition-colors"
          @click="searchOpen = !searchOpen"
          title="Search"
        >
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>

        <!-- My Account -->
        <div class="relative">
          <button 
            class="text-white bg-purple-800 hover:bg-purple-900 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            @click="accountDropdownOpen = !accountDropdownOpen"
            @click.away="accountDropdownOpen = false"
          >
            My Account
          </button>
          
          <!-- Login Dropdown -->
          <div 
            x-show="accountDropdownOpen" 
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 scale-95"
            x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95"
            class="absolute right-0 mt-2 w-72 bg-white border rounded-lg shadow-lg z-50"
            x-cloak
          >
            <form class="px-6 py-4 space-y-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Sign In</h3>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input 
                  type="email" 
                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
                  placeholder="Enter your email"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <input 
                  type="password" 
                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
                  placeholder="Enter your password"
                />
              </div>
              
              <div class="flex items-center justify-between">
                <label class="flex items-center">
                  <input type="checkbox" class="rounded text-purple-600 focus:ring-purple-500">
                  <span class="ml-2 text-sm text-gray-600">Remember me</span>
                </label>
                <a href="/forgot-password" class="text-sm text-purple-600 hover:text-purple-800">Forgot password?</a>
              </div>
              
              <button type="submit" class="w-full bg-purple-800 hover:bg-purple-900 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Sign In
              </button>
              
              <div class="text-center pt-2 border-t">
                <span class="text-sm text-gray-600">Don't have an account? </span>
                <a href="/register" class="text-sm text-purple-600 hover:text-purple-800 font-medium">Create Account</a>
              </div>
            </form>
          </div>
        </div>

      </div>

      <!-- Mobile menu button -->
      <button 
        class="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
        @click="mobileMenuOpen = !mobileMenuOpen"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

    </div>

    <!-- Global Search Bar -->
    <div 
      x-show="searchOpen" 
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 -translate-y-2"
      x-transition:enter-end="opacity-100 translate-y-0"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 translate-y-0"
      x-transition:leave-end="opacity-0 -translate-y-2"
      class="mt-4 border-t pt-4"
      x-cloak
    >
      <form class="flex space-x-2">
        <input 
          type="text" 
          placeholder="Search stations, routes, or operators..."
          class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
        />
        <button 
          type="submit"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
        >
          Search
        </button>
      </form>
    </div>

    <!-- Mobile Menu -->
    <div 
      x-show="mobileMenuOpen" 
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 -translate-y-2"
      x-transition:enter-end="opacity-100 translate-y-0"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 translate-y-0"
      x-transition:leave-end="opacity-0 -translate-y-2"
      class="lg:hidden mt-4 border-t pt-4"
      x-cloak
    >
      <div class="space-y-2">
        
        <!-- About (Mobile) -->
        <div x-data="{ open: false }">
          <button 
            @click="open = !open"
            class="flex items-center justify-between w-full px-3 py-2 text-left hover:bg-gray-50 rounded-lg transition-colors"
          >
            <span class="font-medium">About</span>
            <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div x-show="open" class="ml-4 space-y-1">
            <a href="/about/about-us" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">About Us</a>
            <a href="/about/why-intercity-zambia" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">Why Intercity Zambia</a>
            <a href="/about/partners" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">Our Partners</a>
          </div>
        </div>

        <!-- Other menu items -->
        <a href="/stations" class="block px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors">Stations</a>
        <a href="/routes" class="block px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors">Routes</a>
        <a href="/bus-operators" class="block px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors">Bus Operators</a>
        
        <!-- Destinations (Mobile) -->
        <div x-data="{ open: false }">
          <button 
            @click="open = !open"
            class="flex items-center justify-between w-full px-3 py-2 text-left hover:bg-gray-50 rounded-lg transition-colors"
          >
            <span class="font-medium">Destinations</span>
            <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div x-show="open" class="ml-4 space-y-1">
            <a href="/destinations/city-breaks" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">City Breaks</a>
            <a href="/destinations/all" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">All Destinations</a>
            <a href="/destinations/accommodation" class="block px-3 py-2 text-sm hover:bg-gray-50 rounded-lg transition-colors">Accommodation</a>
          </div>
        </div>

        <!-- Mobile Account Action -->
        <div class="border-t pt-4 mt-4">
          <a 
            href="/login" 
            class="block w-full text-center bg-purple-800 hover:bg-purple-900 text-white px-4 py-3 rounded-lg font-medium transition-colors"
          >
            Sign In / Register
          </a>
        </div>

      </div>
    </div>

  </div>
</nav>




