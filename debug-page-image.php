<?php
/**
 * Debug Page Image - Temporary file to test page image functionality
 * Add ?debug_page_image=1 to any page URL to see debug info
 */

// Only run for logged-in users who can edit posts
if (!is_user_logged_in() || !current_user_can('edit_posts') || !isset($_GET['debug_page_image'])) {
    return;
}

// Get current page ID
$page_id = get_the_ID();

if (!$page_id) {
    echo '<div style="background: #f00; color: #fff; padding: 10px; margin: 10px;">No page ID found</div>';
    return;
}

echo '<div style="background: #000; color: #fff; padding: 20px; margin: 20px; font-family: monospace;">';
echo '<h3>PAGE IMAGE DEBUG INFO</h3>';
echo '<p><strong>Page ID:</strong> ' . $page_id . '</p>';
echo '<p><strong>Page Title:</strong> ' . get_the_title($page_id) . '</p>';

// Check if ACF is available
echo '<p><strong>ACF Available:</strong> ' . (function_exists('get_field') ? 'YES' : 'NO') . '</p>';

if (function_exists('get_field')) {
    // Get page image field
    $page_image = get_field('page_image', $page_id);
    
    echo '<p><strong>Page Image Field Value:</strong> ';
    if (empty($page_image)) {
        echo 'EMPTY/NULL';
    } elseif (is_array($page_image)) {
        echo 'ARRAY - ' . print_r($page_image, true);
    } else {
        echo 'OTHER: ' . var_export($page_image, true);
    }
    echo '</p>';
    
    // Check if featured image exists
    echo '<p><strong>Has Featured Image:</strong> ' . (has_post_thumbnail($page_id) ? 'YES' : 'NO') . '</p>';
    
    if (has_post_thumbnail($page_id)) {
        $featured_image = get_post_thumbnail_id($page_id);
        echo '<p><strong>Featured Image ID:</strong> ' . $featured_image . '</p>';
    }
    
} else {
    echo '<p style="color: #f00;"><strong>ACF NOT AVAILABLE!</strong></p>';
    echo '<p>Make sure Advanced Custom Fields or Secure Custom Fields plugin is installed and activated.</p>';
}

// Check WordPress image sizes
echo '<p><strong>Available Image Sizes:</strong></p>';
echo '<ul>';
foreach (get_intermediate_image_sizes() as $size) {
    echo '<li>' . $size . '</li>';
}
echo '</ul>';

echo '</div>';
?>