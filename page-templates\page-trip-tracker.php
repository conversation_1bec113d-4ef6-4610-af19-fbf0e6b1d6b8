<?php
/**
 * Template Name: <PERSON> Tracker
 * 
 * Real-time tracker interface for bus services
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- <PERSON> Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('Trip Tracker', 'intercityzambia'); ?>
                <span class="inline-flex items-center ml-3">
                    <span class="animate-pulse w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                    <span class="text-sm text-green-600 font-medium font-noto-sans">LIVE</span>
                </span>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Track your bus in real-time. Get live updates on location, estimated arrival times, and service status.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Trip Tracking Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-12">
            <div class="text-center mb-6">
                <h2 class="text-xl md:text-2xl font-bold text-gray-900 font-noto-serif">
                    <?php _e('Track Your Trip', 'intercityzambia'); ?>
                </h2>
                <p class="text-gray-600 mt-2 font-noto-sans">
                    <?php _e('Enter your booking reference or service details to track your bus', 'intercityzambia'); ?>
                </p>
            </div>

            <form method="GET" action="<?php echo home_url('/trip-tracker'); ?>" class="space-y-6" 
                  x-data="{ 
                    trackingMethod: 'booking',
                    bookingRef: '<?php echo esc_attr(get_query_var('booking_ref', '')); ?>',
                    serviceId: '<?php echo esc_attr(get_query_var('service_id', '')); ?>',
                    travelDate: '<?php echo esc_attr(get_query_var('travel_date', '')); ?>'
                  }">
                
                <!-- Tracking Method Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3 font-noto-sans">
                        <?php _e('How would you like to track your trip?', 'intercityzambia'); ?>
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                               :class="trackingMethod === 'booking' ? 'border-primary-600 bg-primary-50' : 'border-gray-300'">
                            <input type="radio" name="method" value="booking" x-model="trackingMethod" class="sr-only">
                            <div class="flex items-center">
                                <div class="w-4 h-4 border-2 rounded-full mr-3 flex items-center justify-center"
                                     :class="trackingMethod === 'booking' ? 'border-primary-600' : 'border-gray-300'">
                                    <div class="w-2 h-2 bg-primary-600 rounded-full" 
                                         x-show="trackingMethod === 'booking'"></div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900 font-noto-serif">
                                        <?php _e('Booking Reference', 'intercityzambia'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600 font-noto-sans">
                                        <?php _e('Use your booking confirmation number', 'intercityzambia'); ?>
                                    </div>
                                </div>
                            </div>
                        </label>
                        
                        <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                               :class="trackingMethod === 'service' ? 'border-primary-600 bg-primary-50' : 'border-gray-300'">
                            <input type="radio" name="method" value="service" x-model="trackingMethod" class="sr-only">
                            <div class="flex items-center">
                                <div class="w-4 h-4 border-2 rounded-full mr-3 flex items-center justify-center"
                                     :class="trackingMethod === 'service' ? 'border-primary-600' : 'border-gray-300'">
                                    <div class="w-2 h-2 bg-primary-600 rounded-full" 
                                         x-show="trackingMethod === 'service'"></div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900 font-noto-serif">
                                        <?php _e('Service Details', 'intercityzambia'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600 font-noto-sans">
                                        <?php _e('Select service and travel date', 'intercityzambia'); ?>
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Booking Reference Input -->
                <div x-show="trackingMethod === 'booking'" x-transition>
                    <label for="booking-ref" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                        <?php _e('Booking Reference Number', 'intercityzambia'); ?>
                    </label>
                    <input 
                        type="text" 
                        id="booking-ref" 
                        name="booking_ref" 
                        x-model="bookingRef"
                        placeholder="<?php _e('Enter your booking reference (e.g., IC-2024-001234)', 'intercityzambia'); ?>"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        :required="trackingMethod === 'booking'"
                    >
                </div>

                <!-- Service Selection -->
                <div x-show="trackingMethod === 'service'" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="service-select" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                                <?php _e('Select Service', 'intercityzambia'); ?>
                            </label>
                            <select 
                                id="service-select" 
                                name="service_id" 
                                x-model="serviceId"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                                :required="trackingMethod === 'service'"
                            >
                                <option value=""><?php _e('Select a service', 'intercityzambia'); ?></option>
                                <?php
                                $services = get_posts(array(
                                    'post_type' => 'service',
                                    'posts_per_page' => -1,
                                    'meta_query' => array(
                                        array(
                                            'key' => 'status',
                                            'value' => 'active',
                                            'compare' => '='
                                        )
                                    )
                                ));
                                
                                foreach ($services as $service) {
                                    $route = get_field('route', $service->ID);
                                    $bus_operator = get_field('bus_operator', $service->ID);
                                    $selected = (get_query_var('service_id') == $service->ID) ? 'selected' : '';
                                    $display_name = $route ? $route->post_title : $service->post_title;
                                    if ($bus_operator) {
                                        $display_name = $bus_operator->post_title . ' - ' . $display_name;
                                    }
                                    echo '<option value="' . esc_attr($service->ID) . '" ' . $selected . '>' . esc_html($display_name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div>
                            <label for="travel-date" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                                <?php _e('Travel Date', 'intercityzambia'); ?>
                            </label>
                            <input 
                                type="date" 
                                id="travel-date" 
                                name="travel_date" 
                                x-model="travelDate"
                                min="<?php echo date('Y-m-d', strtotime('-7 days')); ?>"
                                max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                                :required="trackingMethod === 'service'"
                            >
                        </div>
                    </div>
                </div>

                <!-- Track Button -->
                <div class="text-center">
                    <button 
                        type="submit" 
                        class="bg-primary-800 hover:bg-primary-900 text-white px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200 font-noto-sans"
                    >
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <?php _e('Track My Trip', 'intercityzambia'); ?>
                    </button>
                </div>
            </form>
        </div>

        <?php
        // Handle tracking results
        $booking_ref = get_query_var('booking_ref');
        $service_id = get_query_var('service_id');
        $travel_date = get_query_var('travel_date');
        $showing_tracking_results = !empty($booking_ref) || !empty($service_id);
        
        if ($showing_tracking_results) :
            // Mock tracking data - in real implementation, this would come from GPS tracking system
            $tracking_data = array(
                'status' => 'in_transit',
                'current_location' => 'Kabwe',
                'progress' => 65,
                'estimated_arrival' => '14:30',
                'next_stop' => 'Lusaka Central Station',
                'delay_minutes' => 0,
                'last_updated' => current_time('mysql')
            );
        ?>
            <!-- Tracking Results -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-12">
                <!-- Status Header -->
                <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold font-noto-serif">
                                <?php _e('Trip Status: In Transit', 'intercityzambia'); ?>
                            </h3>
                            <p class="text-green-100 font-noto-sans">
                                <?php printf(__('Last updated: %s', 'intercityzambia'), date('H:i', strtotime($tracking_data['last_updated']))); ?>
                            </p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold font-noto-serif">
                                <?php echo esc_html($tracking_data['estimated_arrival']); ?>
                            </div>
                            <div class="text-sm text-green-100 font-noto-sans">
                                <?php _e('Est. Arrival', 'intercityzambia'); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700 font-noto-sans">
                            <?php _e('Journey Progress', 'intercityzambia'); ?>
                        </span>
                        <span class="text-sm font-medium text-gray-700 font-noto-sans">
                            <?php echo esc_html($tracking_data['progress']); ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-green-500 h-3 rounded-full transition-all duration-300" 
                             style="width: <?php echo esc_attr($tracking_data['progress']); ?>%"></div>
                    </div>
                </div>

                <!-- Trip Details -->
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-sm text-gray-500 font-noto-sans">
                                    <?php _e('Current Location', 'intercityzambia'); ?>
                                </div>
                                <div class="font-medium text-gray-900 font-noto-serif">
                                    <?php echo esc_html($tracking_data['current_location']); ?>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-sm text-gray-500 font-noto-sans">
                                    <?php _e('Next Stop', 'intercityzambia'); ?>
                                </div>
                                <div class="font-medium text-gray-900 font-noto-serif">
                                    <?php echo esc_html($tracking_data['next_stop']); ?>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="text-sm text-gray-500 font-noto-sans">
                                    <?php _e('Status', 'intercityzambia'); ?>
                                </div>
                                <div class="font-medium text-gray-900 font-noto-serif">
                                    <?php 
                                    if ($tracking_data['delay_minutes'] > 0) {
                                        printf(__('Delayed by %d minutes', 'intercityzambia'), $tracking_data['delay_minutes']);
                                    } else {
                                        _e('On Time', 'intercityzambia');
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map Placeholder -->
                <div class="border-t border-gray-200">
                    <div class="h-64 bg-gray-100 flex items-center justify-center">
                        <div class="text-center text-gray-600">
                            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
                            </svg>
                            <p class="font-noto-sans"><?php _e('Live Map Integration', 'intercityzambia'); ?></p>
                            <p class="text-sm text-gray-500 font-noto-sans"><?php _e('Real-time bus location on map', 'intercityzambia'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="p-6 border-t border-gray-200 bg-gray-50">
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <button onclick="refreshTracking()" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <?php _e('Refresh', 'intercityzambia'); ?>
                        </button>
                        <button onclick="shareLocation()" class="bg-secondary-600 hover:bg-secondary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors font-noto-sans">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                            </svg>
                            <?php _e('Share Location', 'intercityzambia'); ?>
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- How It Works -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 md:p-8 mb-12">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                <?php _e('How Trip Tracking Works', 'intercityzambia'); ?>
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl font-noto-serif">1</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2 font-noto-serif">
                        <?php _e('Enter Details', 'intercityzambia'); ?>
                    </h3>
                    <p class="text-gray-600 font-noto-sans">
                        <?php _e('Use your booking reference or select your service and travel date', 'intercityzambia'); ?>
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-secondary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl font-noto-serif">2</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2 font-noto-serif">
                        <?php _e('Real-time Updates', 'intercityzambia'); ?>
                    </h3>
                    <p class="text-gray-600 font-noto-sans">
                        <?php _e('Get live location updates, arrival estimates, and service notifications', 'intercityzambia'); ?>
                    </p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl font-noto-serif">3</span>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2 font-noto-serif">
                        <?php _e('Stay Informed', 'intercityzambia'); ?>
                    </h3>
                    <p class="text-gray-600 font-noto-sans">
                        <?php _e('Share your location with family and get notified about any delays', 'intercityzambia'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Live Services -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                <?php _e('Live Services', 'intercityzambia'); ?>
                <span class="ml-2 text-sm text-green-600 font-medium font-noto-sans">
                    <span class="animate-pulse w-2 h-2 bg-green-500 rounded-full inline-block mr-1"></span>
                    <?php _e('Currently Tracking', 'intercityzambia'); ?>
                </span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php
                // Mock live services data
                $live_services = array(
                    array('route' => 'Lusaka - Livingstone', 'operator' => 'Intercity Express', 'status' => 'In Transit', 'eta' => '14:30'),
                    array('route' => 'Ndola - Kitwe', 'operator' => 'Power Tools', 'status' => 'On Time', 'eta' => '15:45'),
                    array('route' => 'Kabwe - Lusaka', 'operator' => 'Mazhandu', 'status' => 'Delayed 15min', 'eta' => '16:20'),
                    array('route' => 'Chipata - Lusaka', 'operator' => 'Juldan Motors', 'status' => 'In Transit', 'eta' => '17:00'),
                    array('route' => 'Solwezi - Lusaka', 'operator' => 'CR Holdings', 'status' => 'On Time', 'eta' => '18:30'),
                    array('route' => 'Kasama - Lusaka', 'operator' => 'Shalom', 'status' => 'In Transit', 'eta' => '19:15'),
                );
                
                foreach ($live_services as $service) :
                ?>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900 font-noto-serif">
                                <?php echo esc_html($service['route']); ?>
                            </h3>
                            <span class="text-sm px-2 py-1 rounded-full <?php echo strpos($service['status'], 'Delayed') !== false ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'; ?> font-noto-sans">
                                <?php echo esc_html($service['status']); ?>
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 font-noto-sans mb-2">
                            <?php echo esc_html($service['operator']); ?>
                        </p>
                        <p class="text-sm text-gray-500 font-noto-sans">
                            <?php _e('ETA:', 'intercityzambia'); ?> <?php echo esc_html($service['eta']); ?>
                        </p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<script>
function refreshTracking() {
    // Simulate refresh
    window.location.reload();
}

function shareLocation() {
    if (navigator.share) {
        navigator.share({
            title: '<?php _e('My Bus Location', 'intercityzambia'); ?>',
            text: '<?php _e('Track my bus journey in real-time', 'intercityzambia'); ?>',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(window.location.href);
        alert('<?php _e('Link copied to clipboard!', 'intercityzambia'); ?>');
    }
}

// Auto-refresh every 30 seconds when tracking is active
<?php if ($showing_tracking_results) : ?>
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshTracking();
    }
}, 30000);
<?php endif; ?>
</script>

<?php get_footer(); ?>