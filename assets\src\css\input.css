/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Noto+Serif:ital,wght@0,100..900;1,100..900&display=swap');

/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer customizations */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Noto Sans', sans-serif;
    line-height: 1.6;
    color: theme('colors.neutral.800');
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Serif', serif;
    font-weight: 600;
    line-height: 1.2;
    color: theme('colors.primary.800');
  }
  
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.75rem; }
  h4 { font-size: 1.5rem; }
  h5 { font-size: 1.25rem; }
  h6 { font-size: 1.125rem; }
  
  a {
    color: theme('colors.primary.600');
    text-decoration: none;
    transition: color 0.2s ease;
  }
  
  a:hover {
    color: theme('colors.primary.800');
  }
  
  /* WordPress specific styles */
  .wp-block-image {
    margin-bottom: 1.5rem;
  }
  
  .wp-block-quote {
    border-left: 4px solid theme('colors.primary.500');
    padding-left: 1rem;
    margin: 2rem 0;
    font-style: italic;
  }
}

/* Component layer for reusable patterns */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  /* Card components */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-neutral-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 bg-neutral-50 border-t border-neutral-200;
  }
  
  /* Bus booking specific components */
  .route-card {
    @apply card hover:shadow-lg transition-shadow duration-200 cursor-pointer;
  }
  
  .route-card:hover {
    @apply shadow-xl;
  }
  
  .bus-operator-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary-100 text-secondary-800;
  }
  
  .price-tag {
    @apply text-2xl font-bold text-primary-600;
  }
  
  .departure-time {
    @apply text-lg font-semibold text-neutral-700;
  }
  
  /* Form components */
  .form-input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-2;
  }
  
  .form-select {
    @apply form-input appearance-none bg-white;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply block px-4 py-2 text-neutral-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply text-primary-600 bg-primary-50;
  }
  
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Site wrapper styles */
  #site-wrapper {
    @apply transition-all duration-300;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  #site-wrapper:hover {
    @apply shadow-2xl;
  }
  
  /* Animation utilities */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* Gradient utilities for brand colors */
  .bg-gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.800'));
  }
  
  .bg-gradient-secondary {
    background: linear-gradient(135deg, theme('colors.secondary.600'), theme('colors.secondary.800'));
  }
  
  .bg-gradient-brand {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.secondary.600'));
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: theme('colors.neutral.100');
}

::-webkit-scrollbar-thumb {
  background: theme('colors.primary.400');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.primary.600');
}