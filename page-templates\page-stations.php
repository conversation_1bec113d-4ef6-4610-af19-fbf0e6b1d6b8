<?php
/**
 * Template Name: Stations Directory
 * 
 * Displays all intercity stations as cards with photos, names, and descriptions.
 * Also shows compact province cards that expand to show stations by province.
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('Bus Stations', 'intercityzambia'); ?>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Find bus stations across Zambia. Search by location or browse by province to find the nearest station for your journey.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-12" x-data="{ searchTerm: '', selectedProvince: 'all' }">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <label for="station-search" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                        <?php _e('Search Stations', 'intercityzambia'); ?>
                    </label>
                    <input 
                        type="text" 
                        id="station-search"
                        x-model="searchTerm"
                        placeholder="<?php _e('Enter station name or city...', 'intercityzambia'); ?>"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                    >
                </div>
                <div class="md:w-64">
                    <label for="province-filter" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                        <?php _e('Filter by Province', 'intercityzambia'); ?>
                    </label>
                    <select 
                        id="province-filter"
                        x-model="selectedProvince"
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                    >
                        <option value="all"><?php _e('All Provinces', 'intercityzambia'); ?></option>
                        <?php
                        // Get all provinces
                        $provinces = get_posts(array(
                            'post_type' => 'province',
                            'posts_per_page' => -1,
                            'orderby' => 'title',
                            'order' => 'ASC',
                            'meta_query' => array(
                                array(
                                    'key' => 'province_active',
                                    'value' => '1',
                                    'compare' => '='
                                )
                            )
                        ));
                        
                        foreach ($provinces as $province) {
                            echo '<option value="' . esc_attr($province->ID) . '">' . esc_html($province->post_title) . '</option>';
                        }
                        ?>
                    </select>
                </div>
            </div>
        </div>

        <!-- Featured Stations Section -->
        <div class="mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 font-noto-serif">
                <?php _e('Featured Intercity Stations', 'intercityzambia'); ?>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php
                // Get featured stations (main terminals only)
                $featured_stations = get_posts(array(
                    'post_type' => 'station',
                    'posts_per_page' => 9,
                    'orderby' => 'menu_order',
                    'order' => 'ASC',
                    'meta_query' => array(
                        'relation' => 'AND',
                        array(
                            'key' => 'station_active',
                            'value' => '1',
                            'compare' => '='
                        ),
                        array(
                            'key' => 'station_type',
                            'value' => 'main_terminal',
                            'compare' => '='
                        )
                    )
                ));
                
                foreach ($featured_stations as $station) :
                    $station_city = get_field('city', $station->ID);
                    $station_province = $station_city ? get_field('province', $station_city->ID) : null;
                    $station_address = get_field('station_address', $station->ID);
                    $station_description = get_field('station_description', $station->ID);
                ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
                        <!-- Station Image -->
                        <div class="h-48 bg-gray-200 relative overflow-hidden">
                            <?php if (has_post_thumbnail($station->ID)) : ?>
                                <?php echo get_the_post_thumbnail($station->ID, 'medium', array('class' => 'w-full h-full object-cover')); ?>
                            <?php else : ?>
                                <div class="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Station Type Badge -->
                            <div class="absolute top-3 right-3">
                                <span class="bg-primary-800 text-white px-3 py-1 rounded-full text-sm font-medium font-noto-sans">
                                    <?php _e('Main Terminal', 'intercityzambia'); ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Station Info -->
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2 font-noto-serif">
                                <?php echo esc_html($station->post_title); ?>
                            </h3>
                            
                            <div class="flex items-center text-gray-600 mb-3">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="font-noto-sans">
                                    <?php 
                                    if ($station_city) {
                                        echo esc_html($station_city->post_title);
                                        if ($station_province) {
                                            echo ', ' . esc_html($station_province->post_title);
                                        }
                                    }
                                    ?>
                                </span>
                            </div>
                            
                            <?php if ($station_address) : ?>
                                <p class="text-sm text-gray-500 mb-3 font-noto-sans">
                                    <?php echo esc_html($station_address); ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if ($station_description) : ?>
                                <p class="text-gray-600 mb-4 font-noto-sans line-clamp-3">
                                    <?php echo esc_html(wp_trim_words($station_description, 20)); ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="flex items-center justify-between">
                                <a href="<?php echo get_permalink($station->ID); ?>" class="text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                                    <?php _e('View Details', 'intercityzambia'); ?>
                                    <svg class="w-4 h-4 ml-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                                
                                <?php if (get_field('map_location', $station->ID)) : ?>
                                    <button class="text-secondary-600 hover:text-secondary-800 font-medium font-noto-sans">
                                        <?php _e('Map', 'intercityzambia'); ?>
                                        <svg class="w-4 h-4 ml-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                        </svg>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Stations by Province Section -->
        <div class="mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 font-noto-serif">
                <?php _e('Browse by Province', 'intercityzambia'); ?>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" x-data="{ openProvince: null }">
                <?php
                foreach ($provinces as $province) :
                    // Get stations count for this province
                    $stations_count = get_posts(array(
                        'post_type' => 'station',
                        'posts_per_page' => -1,
                        'fields' => 'ids',
                        'meta_query' => array(
                            'relation' => 'AND',
                            array(
                                'key' => 'station_active',
                                'value' => '1',
                                'compare' => '='
                            ),
                            array(
                                'key' => 'city',
                                'value' => wp_list_pluck(get_posts(array(
                                    'post_type' => 'city',
                                    'posts_per_page' => -1,
                                    'fields' => 'ids',
                                    'meta_query' => array(
                                        array(
                                            'key' => 'province',
                                            'value' => $province->ID,
                                            'compare' => '='
                                        )
                                    )
                                )), 'ID'),
                                'compare' => 'IN'
                            )
                        )
                    ));
                    
                    if (empty($stations_count)) continue;
                ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <!-- Province Card -->
                        <div class="p-6 cursor-pointer hover:bg-gray-50 transition-colors duration-200" 
                             @click="openProvince = openProvince === '<?php echo $province->ID; ?>' ? null : '<?php echo $province->ID; ?>'">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h3 class="text-lg font-bold text-gray-900 mb-2 font-noto-serif">
                                        <?php echo esc_html($province->post_title); ?>
                                    </h3>
                                    <p class="text-sm text-gray-600 font-noto-sans">
                                        <?php echo count($stations_count); ?> 
                                        <?php echo _n('station', 'stations', count($stations_count), 'intercityzambia'); ?>
                                    </p>
                                </div>
                                <div class="ml-4">
                                    <svg class="w-5 h-5 text-gray-400 transition-transform duration-200" 
                                         :class="{ 'rotate-180': openProvince === '<?php echo $province->ID; ?>' }" 
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Expanded Station List -->
                        <div x-show="openProvince === '<?php echo $province->ID; ?>'" 
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 max-h-0"
                             x-transition:enter-end="opacity-100 max-h-96"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 max-h-96"
                             x-transition:leave-end="opacity-0 max-h-0"
                             class="border-t border-gray-200 bg-gray-50 overflow-hidden">
                            <div class="p-6 max-h-96 overflow-y-auto">
                                <div class="space-y-3">
                                    <?php
                                    // Get all stations for this province
                                    $province_stations = get_posts(array(
                                        'post_type' => 'station',
                                        'posts_per_page' => -1,
                                        'orderby' => 'title',
                                        'order' => 'ASC',
                                        'meta_query' => array(
                                            'relation' => 'AND',
                                            array(
                                                'key' => 'station_active',
                                                'value' => '1',
                                                'compare' => '='
                                            ),
                                            array(
                                                'key' => 'city',
                                                'value' => wp_list_pluck(get_posts(array(
                                                    'post_type' => 'city',
                                                    'posts_per_page' => -1,
                                                    'fields' => 'ids',
                                                    'meta_query' => array(
                                                        array(
                                                            'key' => 'province',
                                                            'value' => $province->ID,
                                                            'compare' => '='
                                                        )
                                                    )
                                                )), 'ID'),
                                                'compare' => 'IN'
                                            )
                                        )
                                    ));
                                    
                                    foreach ($province_stations as $station) :
                                        $station_city = get_field('city', $station->ID);
                                        $station_type = get_field('station_type', $station->ID);
                                    ?>
                                        <div class="flex items-center justify-between py-2 px-3 bg-white rounded hover:bg-gray-50 transition-colors duration-200">
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-900 font-noto-serif">
                                                    <?php echo esc_html($station->post_title); ?>
                                                </h4>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <span class="font-noto-sans"><?php echo $station_city ? esc_html($station_city->post_title) : ''; ?></span>
                                                    <?php if ($station_type) : ?>
                                                        <span class="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded font-noto-sans">
                                                            <?php echo esc_html(ucfirst(str_replace('_', ' ', $station_type))); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <a href="<?php echo get_permalink($station->ID); ?>" 
                                               class="text-primary-600 hover:text-primary-800 font-medium text-sm font-noto-sans">
                                                <?php _e('View', 'intercityzambia'); ?>
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- All Stations List (Compact) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900 font-noto-serif">
                    <?php _e('All Stations', 'intercityzambia'); ?>
                </h2>
                <p class="text-gray-600 mt-1 font-noto-sans">
                    <?php _e('Complete list of all active bus stations across Zambia', 'intercityzambia'); ?>
                </p>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php
                    // Get all stations
                    $all_stations = get_posts(array(
                        'post_type' => 'station',
                        'posts_per_page' => -1,
                        'orderby' => 'title',
                        'order' => 'ASC',
                        'meta_query' => array(
                            array(
                                'key' => 'station_active',
                                'value' => '1',
                                'compare' => '='
                            )
                        )
                    ));
                    
                    foreach ($all_stations as $station) :
                        $station_city = get_field('city', $station->ID);
                        $station_province = $station_city ? get_field('province', $station_city->ID) : null;
                    ?>
                        <div class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors duration-200">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 font-noto-serif">
                                    <?php echo esc_html($station->post_title); ?>
                                </h4>
                                <div class="text-sm text-gray-600 font-noto-sans">
                                    <?php 
                                    if ($station_city) {
                                        echo esc_html($station_city->post_title);
                                        if ($station_province) {
                                            echo ', ' . esc_html($station_province->post_title);
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <a href="<?php echo get_permalink($station->ID); ?>" 
                               class="text-primary-600 hover:text-primary-800 font-medium text-sm font-noto-sans">
                                <?php _e('View', 'intercityzambia'); ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>