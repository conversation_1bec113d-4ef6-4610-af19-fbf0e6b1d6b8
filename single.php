<?php
/**
 * Default Single Template
 * 
 * This is the default template for single posts and custom post types
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        <div class="max-w-4xl mx-auto">
            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden'); ?>>
                    
                    <!-- Post Header -->
                    <?php if (intercity_has_page_image()) : ?>
                        <div class="relative h-48 md:h-56 lg:h-64 overflow-hidden">
                            <?php 
                            $page_image = intercity_get_page_image();
                            $image_url = isset($page_image['sizes']['page-image']) ? $page_image['sizes']['page-image'] : $page_image['url'];
                            ?>
                            <img src="<?php echo esc_url($image_url); ?>" 
                                 alt="<?php echo esc_attr($page_image['alt']); ?>" 
                                 class="w-full h-full object-cover" 
                                 loading="lazy">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                            <div class="absolute bottom-0 left-0 right-0 p-6 md:p-8">
                                <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2 font-noto-serif">
                                    <?php the_title(); ?>
                                </h1>
                                <?php if (get_the_excerpt()) : ?>
                                    <p class="text-gray-200 font-noto-sans">
                                        <?php echo get_the_excerpt(); ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else : ?>
                        <div class="bg-gradient-to-r from-primary-800 to-primary-600 text-white p-6 md:p-8 lg:p-10">
                            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 font-noto-serif">
                                <?php the_title(); ?>
                            </h1>
                            <?php if (get_the_excerpt()) : ?>
                                <p class="text-primary-100 font-noto-sans">
                                    <?php echo get_the_excerpt(); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Post Content -->
                    <div class="p-6 md:p-8 lg:p-10">
                        <?php if (is_singular('post')) : ?>
                            <!-- Post Meta -->
                            <div class="flex items-center text-sm text-gray-500 mb-6 font-noto-sans">
                                <span><?php echo get_the_date(); ?></span>
                                <span class="mx-2">•</span>
                                <span><?php echo get_the_category_list(', '); ?></span>
                                <?php if (function_exists('intercity_reading_time')) : ?>
                                    <span class="mx-2">•</span>
                                    <span><?php intercity_reading_time(); ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="prose prose-lg max-w-none font-noto-sans">
                            <?php
                            the_content();
                            
                            wp_link_pages(array(
                                'before' => '<div class="page-links mt-8 p-4 bg-gray-50 rounded-lg">',
                                'after'  => '</div>',
                                'link_before' => '<span class="inline-block px-3 py-1 mx-1 bg-primary-800 text-white rounded hover:bg-primary-700 transition-colors">',
                                'link_after' => '</span>',
                            ));
                            ?>
                        </div>

                        <?php if (is_singular('post')) : ?>
                            <!-- Tags -->
                            <?php if (has_tag()) : ?>
                                <div class="mt-8 pt-8 border-t border-gray-200">
                                    <h3 class="text-lg font-semibold mb-4 font-noto-serif">
                                        <?php _e('Tags', 'intercityzambia'); ?>
                                    </h3>
                                    <div class="flex flex-wrap gap-2">
                                        <?php
                                        $tags = get_the_tags();
                                        foreach ($tags as $tag) :
                                        ?>
                                            <a href="<?php echo get_tag_link($tag->term_id); ?>" 
                                               class="inline-block px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors font-noto-sans">
                                                <?php echo esc_html($tag->name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Edit Link -->
                    <?php if (is_user_logged_in() && current_user_can('edit_post', get_the_ID())) : ?>
                        <div class="px-6 md:px-8 lg:px-10 pb-6">
                            <?php edit_post_link(
                                __('Edit Post', 'intercityzambia'),
                                '<p class="text-sm text-gray-500 hover:text-primary-600 transition-colors">',
                                '</p>'
                            ); ?>
                        </div>
                    <?php endif; ?>

                </article>
            <?php endwhile; ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>