<?php
/**
 * Debug Image Cropping Issues
 * 
 * Add this to your functions.php temporarily to debug cropping issues
 */

// Add this to functions.php temporarily to debug
function debug_image_cropping() {
    if (is_admin()) {
        echo '<div class="notice notice-info"><p><strong>Image Cropping Debug Info:</strong><br>';
        
        // Check PHP memory limit
        echo 'PHP Memory Limit: ' . ini_get('memory_limit') . '<br>';
        
        // Check WordPress memory limit
        echo 'WordPress Memory Limit: ' . WP_MEMORY_LIMIT . '<br>';
        
        // Check if GD library is installed
        if (extension_loaded('gd')) {
            echo 'GD Library: Available<br>';
            $gd_info = gd_info();
            echo 'GD Version: ' . $gd_info['GD Version'] . '<br>';
        } else {
            echo 'GD Library: NOT AVAILABLE (Required for image cropping)<br>';
        }
        
        // Check if ImageMagick is available
        if (extension_loaded('imagick')) {
            echo 'ImageMagick: Available<br>';
        } else {
            echo 'ImageMagick: Not Available<br>';
        }
        
        // Check WordPress image editor
        $editor = wp_get_image_editor(ABSPATH . 'wp-includes/images/media/default.png');
        if (is_wp_error($editor)) {
            echo 'WordPress Image Editor: ERROR - ' . $editor->get_error_message() . '<br>';
        } else {
            echo 'WordPress Image Editor: Available (' . get_class($editor) . ')<br>';
        }
        
        // Check upload directory permissions
        $upload_dir = wp_upload_dir();
        echo 'Upload Directory: ' . $upload_dir['basedir'] . '<br>';
        echo 'Upload Directory Writable: ' . (is_writable($upload_dir['basedir']) ? 'YES' : 'NO') . '<br>';
        
        // Check max upload size
        echo 'Max Upload Size: ' . wp_max_upload_size() . ' bytes (' . size_format(wp_max_upload_size()) . ')<br>';
        
        echo '</p></div>';
    }
}
add_action('admin_notices', 'debug_image_cropping');

// Enable WordPress debug logging for images
if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}
if (!defined('WP_DEBUG_LOG')) {
    define('WP_DEBUG_LOG', true);
}

// Log image editor errors
function log_image_editor_errors($editor) {
    if (is_wp_error($editor)) {
        error_log('Image Editor Error: ' . $editor->get_error_message());
    }
    return $editor;
}
add_filter('wp_image_editor_before_change', 'log_image_editor_errors');

// Log detailed image processing
function log_image_processing($image, $attachment_id, $size) {
    error_log('Image Processing - ID: ' . $attachment_id . ', Size: ' . $size);
    return $image;
}
add_filter('wp_get_attachment_image_src', 'log_image_processing', 10, 3);