<?php
/**
 * Template Name: Routes & Fares
 * 
 * List of major routes with estimated durations and fare ranges
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('Routes & Fares', 'intercityzambia'); ?>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Explore our comprehensive network of bus routes across Zambia with transparent pricing and journey information.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Quick Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-12">
            <div class="text-center mb-6">
                <h2 class="text-xl md:text-2xl font-bold text-gray-900 font-noto-serif">
                    <?php _e('Filter Routes', 'intercityzambia'); ?>
                </h2>
                <p class="text-gray-600 mt-2 font-noto-sans">
                    <?php _e('Find routes by departure city, destination, or fare range', 'intercityzambia'); ?>
                </p>
            </div>

            <div class="space-y-4" x-data="{ 
                fromCity: '',
                toCity: '',
                maxFare: '',
                sortBy: 'popularity'
            }">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- From City -->
                    <div>
                        <label for="filter-from" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('From City', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="filter-from" 
                            x-model="fromCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('All Cities', 'intercityzambia'); ?></option>
                            <?php
                            $cities = get_posts(array(
                                'post_type' => 'city',
                                'posts_per_page' => -1,
                                'orderby' => 'title',
                                'order' => 'ASC'
                            ));
                            
                            foreach ($cities as $city) {
                                echo '<option value="' . esc_attr($city->ID) . '">' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- To City -->
                    <div>
                        <label for="filter-to" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('To City', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="filter-to" 
                            x-model="toCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('All Cities', 'intercityzambia'); ?></option>
                            <?php
                            foreach ($cities as $city) {
                                echo '<option value="' . esc_attr($city->ID) . '">' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Max Fare -->
                    <div>
                        <label for="filter-fare" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Max Fare (ZMW)', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="filter-fare" 
                            x-model="maxFare"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('Any Price', 'intercityzambia'); ?></option>
                            <option value="100">K100</option>
                            <option value="200">K200</option>
                            <option value="300">K300</option>
                            <option value="500">K500</option>
                            <option value="1000">K1000+</option>
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div>
                        <label for="filter-sort" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Sort By', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="filter-sort" 
                            x-model="sortBy"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value="popularity"><?php _e('Popularity', 'intercityzambia'); ?></option>
                            <option value="fare_low"><?php _e('Fare: Low to High', 'intercityzambia'); ?></option>
                            <option value="fare_high"><?php _e('Fare: High to Low', 'intercityzambia'); ?></option>
                            <option value="duration"><?php _e('Duration', 'intercityzambia'); ?></option>
                            <option value="distance"><?php _e('Distance', 'intercityzambia'); ?></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Routes -->
        <div class="mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 font-noto-serif">
                <?php _e('Popular Routes', 'intercityzambia'); ?>
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <?php
                // Get popular routes with high priority
                $popular_routes = get_posts(array(
                    'post_type' => 'route',
                    'posts_per_page' => 6,
                    'meta_query' => array(
                        'relation' => 'AND',
                        array(
                            'key' => 'active',
                            'value' => '1',
                            'compare' => '='
                        ),
                        array(
                            'key' => 'priority',
                            'value' => 70,
                            'compare' => '>='
                        )
                    ),
                    'meta_key' => 'priority',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                ));
                
                foreach ($popular_routes as $route) :
                    $departure_city = get_field('departure_city', $route->ID);
                    $arrival_city = get_field('arrival_city', $route->ID);
                    $departure_station = get_field('departure_station', $route->ID);
                    $arrival_station = get_field('arrival_station', $route->ID);
                    $operator = get_field('operator', $route->ID);
                    $distance = get_field('distance_km', $route->ID);
                    $duration = get_field('estimated_duration', $route->ID);
                    $description = get_field('description', $route->ID);
                    
                    // Get fare range from services on this route
                    $route_services = get_posts(array(
                        'post_type' => 'service',
                        'posts_per_page' => -1,
                        'meta_query' => array(
                            array(
                                'key' => 'route',
                                'value' => $route->ID,
                                'compare' => '='
                            )
                        )
                    ));
                    
                    $fares = array();
                    foreach ($route_services as $service) {
                        $fare = get_field('price', $service->ID);
                        if ($fare) {
                            $fares[] = $fare;
                        }
                    }
                    
                    $min_fare = !empty($fares) ? min($fares) : 0;
                    $max_fare = !empty($fares) ? max($fares) : 0;
                ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 font-noto-serif mb-2">
                                    <?php echo esc_html($route->post_title); ?>
                                </h3>
                                <div class="flex items-center text-sm text-gray-600 mb-2">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <span class="font-noto-sans">
                                        <?php echo $operator ? esc_html($operator->post_title) : __('Multiple Operators', 'intercityzambia'); ?>
                                    </span>
                                </div>
                            </div>
                            <span class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium font-noto-sans">
                                <?php _e('Popular', 'intercityzambia'); ?>
                            </span>
                        </div>
                        
                        <!-- Route Visual -->
                        <div class="flex items-center justify-between mb-4 p-4 bg-gray-50 rounded-lg">
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900 font-noto-serif">
                                    <?php echo $departure_city ? esc_html($departure_city->post_title) : ''; ?>
                                </div>
                                <div class="text-sm text-gray-600 font-noto-sans">
                                    <?php echo $departure_station ? esc_html($departure_station->post_title) : ''; ?>
                                </div>
                            </div>
                            
                            <div class="flex items-center px-4">
                                <div class="w-3 h-3 bg-primary-600 rounded-full"></div>
                                <div class="w-16 h-px bg-gray-300 mx-2"></div>
                                <svg class="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="w-16 h-px bg-gray-300 mx-2"></div>
                                <div class="w-3 h-3 bg-secondary-600 rounded-full"></div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900 font-noto-serif">
                                    <?php echo $arrival_city ? esc_html($arrival_city->post_title) : ''; ?>
                                </div>
                                <div class="text-sm text-gray-600 font-noto-sans">
                                    <?php echo $arrival_station ? esc_html($arrival_station->post_title) : ''; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Route Details -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-sm text-gray-500 font-noto-sans"><?php _e('Distance', 'intercityzambia'); ?></div>
                                <div class="font-bold text-gray-900 font-noto-serif">
                                    <?php echo $distance ? esc_html($distance) . ' km' : '—'; ?>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-gray-500 font-noto-sans"><?php _e('Duration', 'intercityzambia'); ?></div>
                                <div class="font-bold text-gray-900 font-noto-serif">
                                    <?php echo $duration ? esc_html($duration) : '—'; ?>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-gray-500 font-noto-sans"><?php _e('Fare Range', 'intercityzambia'); ?></div>
                                <div class="font-bold text-gray-900 font-noto-serif">
                                    <?php 
                                    if ($min_fare && $max_fare) {
                                        if ($min_fare == $max_fare) {
                                            echo 'K' . number_format($min_fare, 0);
                                        } else {
                                            echo 'K' . number_format($min_fare, 0) . ' - K' . number_format($max_fare, 0);
                                        }
                                    } else {
                                        echo '—';
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-sm text-gray-500 font-noto-sans"><?php _e('Services', 'intercityzambia'); ?></div>
                                <div class="font-bold text-gray-900 font-noto-serif">
                                    <?php echo count($route_services); ?> <?php _e('daily', 'intercityzambia'); ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
                            <a href="<?php echo get_permalink($route->ID); ?>" 
                               class="btn-outline text-center flex-1">
                                <?php _e('View Route', 'intercityzambia'); ?>
                            </a>
                            <a href="<?php echo home_url('/find-services?from=' . ($departure_city ? $departure_city->ID : '') . '&to=' . ($arrival_city ? $arrival_city->ID : '')); ?>" 
                               class="btn-primary text-center flex-1">
                                <?php _e('Find Services', 'intercityzambia'); ?>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- All Routes by Province -->
        <div class="mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 font-noto-serif">
                <?php _e('Routes by Province', 'intercityzambia'); ?>
            </h2>
            
            <?php
            // Get all provinces
            $provinces = get_posts(array(
                'post_type' => 'province',
                'posts_per_page' => -1,
                'orderby' => 'title',
                'order' => 'ASC'
            ));
            
            foreach ($provinces as $province) :
                // Get routes that start from cities in this province
                $province_cities = get_posts(array(
                    'post_type' => 'city',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => 'province',
                            'value' => $province->ID,
                            'compare' => '='
                        )
                    )
                ));
                
                $city_ids = wp_list_pluck($province_cities, 'ID');
                
                if (!empty($city_ids)) :
                    $province_routes = get_posts(array(
                        'post_type' => 'route',
                        'posts_per_page' => -1,
                        'meta_query' => array(
                            'relation' => 'AND',
                            array(
                                'key' => 'active',
                                'value' => '1',
                                'compare' => '='
                            ),
                            array(
                                'key' => 'departure_city',
                                'value' => $city_ids,
                                'compare' => 'IN'
                            )
                        )
                    ));
                    
                    if (!empty($province_routes)) :
            ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-6" 
                             x-data="{ expanded: false }">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-bold text-gray-900 font-noto-serif">
                                    <?php echo esc_html($province->post_title); ?>
                                </h3>
                                <button @click="expanded = !expanded" 
                                        class="flex items-center text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                                    <span x-text="expanded ? '<?php _e('Show Less', 'intercityzambia'); ?>' : '<?php _e('Show Routes', 'intercityzambia'); ?>'"></span>
                                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" 
                                         :class="{ 'rotate-180': expanded }"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="text-gray-600 mb-4 font-noto-sans">
                                <?php printf(__('%d routes departing from %s', 'intercityzambia'), count($province_routes), esc_html($province->post_title)); ?>
                            </div>
                            
                            <div x-show="expanded" x-collapse>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                    <?php foreach ($province_routes as $route) :
                                        $departure_city = get_field('departure_city', $route->ID);
                                        $arrival_city = get_field('arrival_city', $route->ID);
                                        $distance = get_field('distance_km', $route->ID);
                                        $duration = get_field('estimated_duration', $route->ID);
                                        
                                        // Get fare range
                                        $route_services = get_posts(array(
                                            'post_type' => 'service',
                                            'posts_per_page' => -1,
                                            'meta_query' => array(
                                                array(
                                                    'key' => 'route',
                                                    'value' => $route->ID,
                                                    'compare' => '='
                                                )
                                            )
                                        ));
                                        
                                        $fares = array();
                                        foreach ($route_services as $service) {
                                            $fare = get_field('price', $service->ID);
                                            if ($fare) {
                                                $fares[] = $fare;
                                            }
                                        }
                                        
                                        $min_fare = !empty($fares) ? min($fares) : 0;
                                        $max_fare = !empty($fares) ? max($fares) : 0;
                                    ?>
                                        <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                                            <div class="flex items-center justify-between mb-2">
                                                <h4 class="font-medium text-gray-900 font-noto-serif">
                                                    <?php echo esc_html($route->post_title); ?>
                                                </h4>
                                                <div class="text-sm text-gray-600 font-noto-sans">
                                                    <?php 
                                                    if ($min_fare && $max_fare) {
                                                        if ($min_fare == $max_fare) {
                                                            echo 'K' . number_format($min_fare, 0);
                                                        } else {
                                                            echo 'K' . number_format($min_fare, 0) . '-' . number_format($max_fare, 0);
                                                        }
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between text-sm text-gray-600">
                                                <span class="font-noto-sans">
                                                    <?php echo ($distance ? esc_html($distance) . ' km' : '') . ($duration ? ' • ' . esc_html($duration) : ''); ?>
                                                </span>
                                                <a href="<?php echo home_url('/find-services?from=' . ($departure_city ? $departure_city->ID : '') . '&to=' . ($arrival_city ? $arrival_city->ID : '')); ?>" 
                                                   class="text-primary-600 hover:text-primary-800 font-medium font-noto-sans">
                                                    <?php _e('Book', 'intercityzambia'); ?>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>

        <!-- Fare Information -->
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 md:p-8">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                <?php _e('Fare Information', 'intercityzambia'); ?>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-3 font-noto-serif">
                        <?php _e('Fare Factors', 'intercityzambia'); ?>
                    </h3>
                    <ul class="space-y-2 text-gray-700 font-noto-sans">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Distance between cities', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Bus type and comfort level', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Travel time and schedule', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Seasonal demand', 'intercityzambia'); ?>
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-3 font-noto-serif">
                        <?php _e('Payment Methods', 'intercityzambia'); ?>
                    </h3>
                    <ul class="space-y-2 text-gray-700 font-noto-sans">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Mobile Money (MTN, Airtel)', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Bank transfers', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Cash at stations', 'intercityzambia'); ?>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <?php _e('Credit/Debit cards', 'intercityzambia'); ?>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <p class="text-sm text-gray-600 font-noto-sans">
                    <strong><?php _e('Note:', 'intercityzambia'); ?></strong> 
                    <?php _e('Fares are subject to change based on fuel prices, seasonal demand, and other factors. All prices shown are in Zambian Kwacha (ZMW). For the most accurate fare information, please check with the specific operator before booking.', 'intercityzambia'); ?>
                </p>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>