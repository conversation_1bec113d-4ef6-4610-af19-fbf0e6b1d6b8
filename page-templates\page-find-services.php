<?php
/**
 * Template Name: Find Services
 * 
 * Searchable list of available services (departure, arrival, date)
 * 
 * @package IntercityZambia
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container-custom py-8 md:py-12 lg:py-16">
        
        <!-- <PERSON> Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-noto-serif">
                <?php _e('Find Services', 'intercityzambia'); ?>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto font-noto-sans">
                <?php _e('Search and book bus services across Zambia. Find the perfect service for your journey with real-time availability.', 'intercityzambia'); ?>
            </p>
        </div>

        <!-- Service Search Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 md:p-8 mb-12">
            <form method="GET" action="<?php echo home_url('/find-services'); ?>" class="space-y-6" 
                  x-data="{ 
                    fromCity: '<?php echo esc_attr(get_query_var('from', '')); ?>',
                    toCity: '<?php echo esc_attr(get_query_var('to', '')); ?>',
                    serviceDate: '<?php echo esc_attr(get_query_var('date', date('Y-m-d'))); ?>',
                    departureTime: '<?php echo esc_attr(get_query_var('departure_time', '')); ?>',
                    busType: '<?php echo esc_attr(get_query_var('bus_type', '')); ?>'
                  }">
                
                <div class="text-center mb-6">
                    <h2 class="text-xl md:text-2xl font-bold text-gray-900 font-noto-serif">
                        <?php _e('Search Bus Services', 'intercityzambia'); ?>
                    </h2>
                    <p class="text-gray-600 mt-2 font-noto-sans">
                        <?php _e('Find available bus services for your travel dates', 'intercityzambia'); ?>
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- From City -->
                    <div>
                        <label for="from-city" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('From', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="from-city" 
                            name="from" 
                            x-model="fromCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                            required
                        >
                            <option value=""><?php _e('Select departure city', 'intercityzambia'); ?></option>
                            <?php
                            $cities = get_posts(array(
                                'post_type' => 'city',
                                'posts_per_page' => -1,
                                'orderby' => 'title',
                                'order' => 'ASC',
                                'meta_query' => array(
                                    array(
                                        'key' => 'city_active',
                                        'value' => '1',
                                        'compare' => '='
                                    )
                                )
                            ));
                            
                            foreach ($cities as $city) {
                                $selected = (get_query_var('from') == $city->ID) ? 'selected' : '';
                                echo '<option value="' . esc_attr($city->ID) . '" ' . $selected . '>' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- To City -->
                    <div>
                        <label for="to-city" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('To', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="to-city" 
                            name="to" 
                            x-model="toCity"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                            required
                        >
                            <option value=""><?php _e('Select arrival city', 'intercityzambia'); ?></option>
                            <?php
                            foreach ($cities as $city) {
                                $selected = (get_query_var('to') == $city->ID) ? 'selected' : '';
                                echo '<option value="' . esc_attr($city->ID) . '" ' . $selected . '>' . esc_html($city->post_title) . '</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Travel Date -->
                    <div>
                        <label for="service-date" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Travel Date', 'intercityzambia'); ?>
                        </label>
                        <input 
                            type="date" 
                            id="service-date" 
                            name="date" 
                            x-model="serviceDate"
                            min="<?php echo date('Y-m-d'); ?>"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                            required
                        >
                    </div>

                    <!-- Departure Time -->
                    <div>
                        <label for="departure-time" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Departure Time', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="departure-time" 
                            name="departure_time" 
                            x-model="departureTime"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('Any time', 'intercityzambia'); ?></option>
                            <option value="morning" <?php echo (get_query_var('departure_time') == 'morning') ? 'selected' : ''; ?>><?php _e('Morning (6:00 - 11:59)', 'intercityzambia'); ?></option>
                            <option value="afternoon" <?php echo (get_query_var('departure_time') == 'afternoon') ? 'selected' : ''; ?>><?php _e('Afternoon (12:00 - 17:59)', 'intercityzambia'); ?></option>
                            <option value="evening" <?php echo (get_query_var('departure_time') == 'evening') ? 'selected' : ''; ?>><?php _e('Evening (18:00 - 23:59)', 'intercityzambia'); ?></option>
                            <option value="night" <?php echo (get_query_var('departure_time') == 'night') ? 'selected' : ''; ?>><?php _e('Night (00:00 - 5:59)', 'intercityzambia'); ?></option>
                        </select>
                    </div>

                    <!-- Bus Type -->
                    <div>
                        <label for="bus-type" class="block text-sm font-medium text-gray-700 mb-2 font-noto-sans">
                            <?php _e('Bus Type', 'intercityzambia'); ?>
                        </label>
                        <select 
                            id="bus-type" 
                            name="bus_type" 
                            x-model="busType"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-600 focus:border-transparent font-noto-sans"
                        >
                            <option value=""><?php _e('Any type', 'intercityzambia'); ?></option>
                            <option value="standard" <?php echo (get_query_var('bus_type') == 'standard') ? 'selected' : ''; ?>><?php _e('Standard', 'intercityzambia'); ?></option>
                            <option value="express" <?php echo (get_query_var('bus_type') == 'express') ? 'selected' : ''; ?>><?php _e('Express', 'intercityzambia'); ?></option>
                            <option value="luxury" <?php echo (get_query_var('bus_type') == 'luxury') ? 'selected' : ''; ?>><?php _e('Luxury', 'intercityzambia'); ?></option>
                            <option value="vip" <?php echo (get_query_var('bus_type') == 'vip') ? 'selected' : ''; ?>><?php _e('VIP', 'intercityzambia'); ?></option>
                        </select>
                    </div>

                    <!-- Search Button -->
                    <div class="flex items-end">
                        <button 
                            type="submit" 
                            class="w-full bg-primary-800 hover:bg-primary-900 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 font-noto-sans"
                        >
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <?php _e('Search', 'intercityzambia'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <?php
        // Handle search results
        $search_from = get_query_var('from');
        $search_to = get_query_var('to');
        $search_date = get_query_var('date');
        $search_departure_time = get_query_var('departure_time');
        $search_bus_type = get_query_var('bus_type');
        $showing_search_results = !empty($search_from) || !empty($search_to) || !empty($search_date);
        
        if ($showing_search_results) {
            // Build meta query for search
            $meta_query = array('relation' => 'AND');
            
            if (!empty($search_from)) {
                $meta_query[] = array(
                    'key' => 'departure_city',
                    'value' => $search_from,
                    'compare' => '='
                );
            }
            
            if (!empty($search_to)) {
                $meta_query[] = array(
                    'key' => 'arrival_city',
                    'value' => $search_to,
                    'compare' => '='
                );
            }
            
            if (!empty($search_date)) {
                $meta_query[] = array(
                    'key' => 'service_date',
                    'value' => $search_date,
                    'compare' => '='
                );
            }
            
            // Add active service filter
            $meta_query[] = array(
                'key' => 'status',
                'value' => 'active',
                'compare' => '='
            );
            
            // Get search results
            $services = get_posts(array(
                'post_type' => 'service',
                'posts_per_page' => 20,
                'meta_query' => $meta_query,
                'meta_key' => 'departure_times',
                'orderby' => 'meta_value',
                'order' => 'ASC'
            ));
        } else {
            // Get today's services
            $services = get_posts(array(
                'post_type' => 'service',
                'posts_per_page' => 10,
                'meta_query' => array(
                    array(
                        'key' => 'status',
                        'value' => 'active',
                        'compare' => '='
                    ),
                    array(
                        'key' => 'service_date',
                        'value' => date('Y-m-d'),
                        'compare' => '>='
                    )
                ),
                'meta_key' => 'departure_times',
                'orderby' => 'meta_value',
                'order' => 'ASC'
            ));
        }
        ?>

        <!-- Services Results -->
        <div class="mb-16">
            <div class="flex items-center justify-between mb-8">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 font-noto-serif">
                    <?php echo $showing_search_results ? __('Search Results', 'intercityzambia') : __('Today\'s Services', 'intercityzambia'); ?>
                </h2>
                <div class="text-sm text-gray-600 font-noto-sans">
                    <?php printf(__('%d services found', 'intercityzambia'), count($services)); ?>
                </div>
            </div>

            <?php if (empty($services)) : ?>
                <div class="text-center py-12">
                    <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-xl font-bold text-gray-900 mb-2 font-noto-serif">
                        <?php _e('No Services Found', 'intercityzambia'); ?>
                    </h3>
                    <p class="text-gray-600 mb-6 font-noto-sans">
                        <?php _e('No services match your search criteria. Try adjusting your filters or check back later.', 'intercityzambia'); ?>
                    </p>
                </div>
            <?php else : ?>
                <div class="space-y-6">
                    <?php foreach ($services as $service) :
                        $route = get_field('route', $service->ID);
                        $bus_operator = get_field('bus_operator', $service->ID);
                        $bus = get_field('bus', $service->ID);
                        $departure_times = get_field('departure_times', $service->ID);
                        $service_date = get_field('service_date', $service->ID);
                        $price = get_field('price', $service->ID);
                        $status = get_field('status', $service->ID);
                        
                        $departure_city = $route ? get_field('departure_city', $route->ID) : null;
                        $arrival_city = $route ? get_field('arrival_city', $route->ID) : null;
                        $departure_station = $route ? get_field('departure_station', $route->ID) : null;
                        $arrival_station = $route ? get_field('arrival_station', $route->ID) : null;
                    ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-300">
                            <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                                
                                <!-- Service Info -->
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <h3 class="text-lg font-bold text-gray-900 font-noto-serif mr-3">
                                                <?php echo $bus_operator ? esc_html($bus_operator->post_title) : __('Unknown Operator', 'intercityzambia'); ?>
                                            </h3>
                                            <span class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium font-noto-sans">
                                                <?php echo $bus ? esc_html(get_field('bus_type', $bus->ID)) : __('Standard', 'intercityzambia'); ?>
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-500 font-noto-sans">
                                            <?php echo esc_html(date('M j, Y', strtotime($service_date))); ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Route Details -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <div class="flex items-center text-gray-600 mb-2">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="font-medium font-noto-sans"><?php _e('From:', 'intercityzambia'); ?></span>
                                            </div>
                                            <p class="text-gray-900 font-noto-sans">
                                                <?php echo $departure_city ? esc_html($departure_city->post_title) : ''; ?>
                                                <?php if ($departure_station) : ?>
                                                    <span class="text-gray-600 text-sm block">
                                                        <?php echo esc_html($departure_station->post_title); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <div class="flex items-center text-gray-600 mb-2">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="font-medium font-noto-sans"><?php _e('To:', 'intercityzambia'); ?></span>
                                            </div>
                                            <p class="text-gray-900 font-noto-sans">
                                                <?php echo $arrival_city ? esc_html($arrival_city->post_title) : ''; ?>
                                                <?php if ($arrival_station) : ?>
                                                    <span class="text-gray-600 text-sm block">
                                                        <?php echo esc_html($arrival_station->post_title); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <!-- Departure Times -->
                                    <?php if ($departure_times && is_array($departure_times)) : ?>
                                        <div class="mb-4">
                                            <div class="flex items-center text-gray-600 mb-2">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="font-medium font-noto-sans"><?php _e('Departure Times:', 'intercityzambia'); ?></span>
                                            </div>
                                            <div class="flex flex-wrap gap-2">
                                                <?php foreach ($departure_times as $time_slot) : ?>
                                                    <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-noto-sans">
                                                        <?php echo esc_html($time_slot['departure_time']); ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Price and Actions -->
                                <div class="flex flex-col sm:flex-row gap-3 lg:flex-col lg:w-48">
                                    <div class="text-center lg:mb-4">
                                        <div class="text-2xl font-bold text-primary-800 font-noto-serif">
                                            <?php echo $price ? 'K' . number_format($price, 2) : __('Price on request', 'intercityzambia'); ?>
                                        </div>
                                        <div class="text-sm text-gray-500 font-noto-sans">
                                            <?php _e('per person', 'intercityzambia'); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-col gap-2">
                                        <a href="<?php echo get_permalink($service->ID); ?>" 
                                           class="btn-primary text-center">
                                            <?php _e('View Details', 'intercityzambia'); ?>
                                        </a>
                                        <a href="<?php echo home_url('/book?service=' . $service->ID); ?>" 
                                           class="bg-secondary-600 hover:bg-secondary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors text-center font-noto-sans">
                                            <?php _e('Book Now', 'intercityzambia'); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Popular Routes -->
        <?php if (!$showing_search_results) : ?>
            <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6 md:p-8">
                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6 font-noto-serif">
                    <?php _e('Popular Routes', 'intercityzambia'); ?>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php
                    $popular_routes = get_posts(array(
                        'post_type' => 'route',
                        'posts_per_page' => 6,
                        'meta_query' => array(
                            array(
                                'key' => 'active',
                                'value' => '1',
                                'compare' => '='
                            ),
                            array(
                                'key' => 'priority',
                                'value' => 50,
                                'compare' => '>='
                            )
                        ),
                        'meta_key' => 'priority',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC'
                    ));
                    
                    foreach ($popular_routes as $route) :
                        $departure_city = get_field('departure_city', $route->ID);
                        $arrival_city = get_field('arrival_city', $route->ID);
                    ?>
                        <a href="<?php echo add_query_arg(array('from' => $departure_city->ID, 'to' => $arrival_city->ID), home_url('/find-services')); ?>" 
                           class="block bg-white rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                            <h3 class="font-medium text-gray-900 font-noto-serif mb-2">
                                <?php echo esc_html($route->post_title); ?>
                            </h3>
                            <p class="text-sm text-gray-600 font-noto-sans">
                                <?php _e('Search services', 'intercityzambia'); ?>
                            </p>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php get_footer(); ?>